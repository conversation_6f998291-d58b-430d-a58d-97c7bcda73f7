/**
 * Test script for Momentum Building Entry + Hybrid Exit Strategy
 * Tests the new 0.30% momentum building entry and 50%/50% hybrid exit system
 */

const VelocityEntryDetector = require('./src/velocity-entry-detector');
const PaperTradingEngine = require('./src/paper-trading-engine');

class MomentumHybridTester {
    constructor() {
        this.detector = new VelocityEntryDetector();
        this.engine = new PaperTradingEngine();
        
        console.log('🧪 MOMENTUM BUILDING + HYBRID EXIT TESTER');
        console.log('==========================================');
        console.log('Entry: 0.10% → 0.20% → 0.30% progression + 15% pressure + 8EMA + spread');
        console.log('Exit: 50% at 1.20% + 50% momentum-based (velocity <0.20% for 2 seconds)');
        console.log('');
    }

    /**
     * Test momentum building entry detection
     */
    testMomentumEntry() {
        console.log('📈 TESTING MOMENTUM BUILDING ENTRY');
        console.log('==================================');

        // Simulate price progression that should trigger momentum building signal
        const symbol = 'TEST-USDT';
        const basePrice = 1.0000;
        
        // Set up mock data
        this.detector.gainerRanks.set(symbol, 3); // Top 3 gainer
        this.detector.priceHistory.set(symbol, []);
        
        // Add price history showing momentum building (corrected for 1-second velocities)
        const priceHistory = [
            { price: basePrice, timestamp: Date.now() - 3000 },         // M3: $1.0000
            { price: basePrice * 1.001, timestamp: Date.now() - 2000 }, // M2: $1.0010 (+0.10% from M3)
            { price: basePrice * 1.001 * 1.002, timestamp: Date.now() - 1000 }, // M1: $1.0030 (+0.20% from M2)
            { price: basePrice * 1.001 * 1.002 * 1.003, timestamp: Date.now() } // M0: $1.0060 (+0.30% from M1)
        ];
        
        this.detector.priceHistory.set(symbol, priceHistory);
        
        // Mock required methods
        this.detector.calculateOrderBookPressure = () => 0.16; // 16% pressure
        this.detector.getCurrentEMA = () => basePrice * 0.999; // Price above EMA
        this.detector.passesSpreadFilter = () => true;
        this.detector.checkVolumeConfirmation = () => true;
        
        // Debug: Calculate velocities manually
        const M0 = priceHistory[3].price;
        const M1 = priceHistory[2].price;
        const M2 = priceHistory[1].price;
        const M3 = priceHistory[0].price;

        const velocity_current = (M0 - M2) / M2;
        const velocity_2sec_ago = (M1 - M3) / M3;
        const velocity_3sec_ago = (M2 - M3) / M3;

        console.log('🔍 DEBUG VELOCITY CALCULATIONS:');
        console.log(`   M3 (3s ago): $${M3.toFixed(6)}`);
        console.log(`   M2 (2s ago): $${M2.toFixed(6)}`);
        console.log(`   M1 (1s ago): $${M1.toFixed(6)}`);
        console.log(`   M0 (current): $${M0.toFixed(6)}`);
        console.log(`   Velocity 3s ago: ${(velocity_3sec_ago * 100).toFixed(3)}% (need ≥0.10%)`);
        console.log(`   Velocity 2s ago: ${(velocity_2sec_ago * 100).toFixed(3)}% (need ≥0.20%)`);
        console.log(`   Velocity current: ${(velocity_current * 100).toFixed(3)}% (need ≥0.30%)`);
        console.log(`   Progression check: ${velocity_current > velocity_2sec_ago && velocity_2sec_ago > velocity_3sec_ago}`);

        // Test signal detection
        const signal = this.detector.checkVelocitySignal(symbol, priceHistory, Date.now());

        if (signal) {
            console.log('✅ MOMENTUM BUILDING SIGNAL DETECTED:');
            console.log(`   Symbol: ${signal.symbol}`);
            console.log(`   Entry Price: $${signal.entryPrice.toFixed(6)}`);
            console.log(`   Confidence: ${signal.confidence}%`);
            console.log(`   Momentum: ${signal.momentumData.progression}`);
            console.log(`   Pressure: ${signal.pressurePercent.toFixed(1)}%`);
            return signal;
        } else {
            console.log('❌ No momentum building signal detected');
            return null;
        }
    }

    /**
     * Test hybrid exit strategy
     */
    testHybridExit() {
        console.log('\n🎯 TESTING HYBRID EXIT STRATEGY');
        console.log('===============================');

        // Enter a position
        const entryPrice = 1.0030;
        const entryResult = this.engine.enterPosition('TEST-USDT', entryPrice, Date.now());
        
        if (!entryResult.success) {
            console.log('❌ Failed to enter position for testing');
            return;
        }

        console.log('✅ Test position entered');
        console.log(`   Entry Price: $${entryPrice.toFixed(6)}`);
        console.log(`   Profit Target: $${this.engine.currentPosition.profitTarget.toFixed(6)} (1.20%)`);
        
        // Test first half exit at 1.20% profit
        const firstExitPrice = entryPrice * 1.012; // 1.20% profit
        console.log(`\n📊 Testing first half exit at $${firstExitPrice.toFixed(6)}...`);
        
        const updateResult1 = this.engine.updatePosition(firstExitPrice, Date.now());
        if (updateResult1.exitCondition && updateResult1.exitCondition.type === 'FIRST_HALF_PROFIT') {
            console.log('✅ First half exit condition triggered');
            
            const exitResult1 = this.engine.exitPosition(
                firstExitPrice,
                updateResult1.exitCondition.reason,
                Date.now(),
                updateResult1.exitCondition
            );
            
            if (exitResult1.success && exitResult1.isPartialExit) {
                console.log('✅ First 50% successfully exited at 1.20% profit');
                console.log(`   Profit: ${exitResult1.trade.profitPercent.toFixed(2)}%`);
                console.log(`   Remaining: 50% position still active`);
            }
        }

        // Test second half momentum exit
        console.log(`\n📉 Testing momentum-based exit for remaining 50%...`);
        
        // Simulate low velocity for 2 consecutive periods
        const position = this.engine.currentPosition;
        if (position) {
            // Add velocity history showing declining momentum
            position.velocityHistory = [1.0150, 1.0152, 1.0153]; // Low velocity progression
            position.consecutiveLowVelocity = 2; // 2 consecutive low velocity periods
            
            const momentumExitPrice = 1.0180; // Higher than first exit
            const updateResult2 = this.engine.updatePosition(momentumExitPrice, Date.now());
            
            if (updateResult2.exitCondition && updateResult2.exitCondition.type === 'MOMENTUM_EXIT') {
                console.log('✅ Momentum exit condition triggered');
                
                const exitResult2 = this.engine.exitPosition(
                    momentumExitPrice,
                    updateResult2.exitCondition.reason,
                    Date.now(),
                    updateResult2.exitCondition
                );
                
                if (exitResult2.success) {
                    console.log('✅ Remaining 50% successfully exited on momentum loss');
                    console.log(`   Exit Price: $${momentumExitPrice.toFixed(6)}`);
                    console.log(`   Additional Gain: ${((momentumExitPrice - firstExitPrice) / firstExitPrice * 100).toFixed(2)}%`);
                    console.log('🎉 HYBRID EXIT STRATEGY COMPLETE');
                }
            }
        }
    }

    /**
     * Run all tests
     */
    runTests() {
        const signal = this.testMomentumEntry();
        
        if (signal) {
            this.testHybridExit();
        }
        
        console.log('\n📋 TEST SUMMARY');
        console.log('===============');
        console.log('✅ Momentum building entry: 0.10% → 0.20% → 0.30% progression');
        console.log('✅ Hybrid exit: 50% at 1.20% + 50% momentum-based');
        console.log('✅ Enhanced logging with momentum data');
        console.log('✅ Partial position tracking');
        console.log('\n🚀 Implementation ready for live testing!');
    }
}

// Run the test
const tester = new MomentumHybridTester();
tester.runTests();
