/**
 * Velocity Position Manager
 * Enhanced position manager for velocity and thrust-based entry signals
 * Handles faster, more aggressive trading with the new entry strategies
 */
class VelocityPositionManager {
    constructor(tradingEngine, velocityDetector) {
        this.tradingEngine = tradingEngine;
        this.velocityDetector = velocityDetector;
        
        // Position monitoring
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.lastPriceUpdate = 0;
        
        // Exit conditions (updated to match paper trading engine)
        this.exitConditions = {
            profitTarget: 0.012, // 1.20% (matches paper trading engine)
            stopLoss: 0.006, // 0.60% (matches paper trading engine)
            maxRedTicks: 3, // Exit after 3 consecutive red ticks
            maxHoldTime: 300000, // 5 minutes max hold time
            gainerRankExit: 15 // Exit if coin drops out of top 15 (more lenient)
        };
        
        // Performance tracking
        this.executedTrades = 0;
        this.missedSignals = 0;
        this.velocityTrades = 0;
        this.thrustTrades = 0;
        this.signalAccuracy = 0;
        
        // Signal validation
        this.minConfidence = 50; // Lower threshold for faster entries
        this.maxRank = 10; // Only trade top 10 gainers

        // Market data for real-time price access
        this.currentTickers = [];
        
        console.log('⚡ Velocity Position Manager initialized');
        console.log(`🎯 Profit Target: ${(this.exitConditions.profitTarget * 100).toFixed(2)}%`);
        console.log(`🛑 Stop Loss: ${(this.exitConditions.stopLoss * 100).toFixed(2)}%`);
        console.log(`📈 8EMA Trend Filter: ENABLED`);
        console.log(`📊 Orderbook Spread Filter: ≤0.13%`);
        console.log(`🚫 Coin Cooldown: 3 minutes`);
        console.log(`💰 Fee Protection: 0.2% minimum exit`);
        console.log(`⚡ Min Confidence: ${this.minConfidence}%`);
    }

    /**
     * Start position monitoring and signal detection
     * @param {number} updateInterval - Update interval in milliseconds
     */
    startMonitoring(updateInterval = 1000) {
        if (this.isMonitoring) {
            console.log('⚠️ Velocity position monitoring already active');
            return;
        }

        this.isMonitoring = true;
        console.log('🔄 Starting velocity position monitoring...');
        
        this.monitoringInterval = setInterval(() => {
            this.monitoringCycle();
        }, updateInterval);
    }

    /**
     * Stop position monitoring
     */
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }

        this.isMonitoring = false;
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        
        console.log('⏹️ Velocity position monitoring stopped');
    }

    /**
     * Main monitoring cycle - called every update interval
     */
    async monitoringCycle() {
        try {
            const timestamp = Date.now();
            
            // Check for new entry signals if not in trade
            if (!this.tradingEngine.isInTrade) {
                await this.checkForVelocitySignals(timestamp);
            } else {
                // Monitor current position
                await this.monitorCurrentPosition(timestamp);
            }
            
            this.lastPriceUpdate = timestamp;
            
        } catch (error) {
            console.error('❌ Error in velocity monitoring cycle:', error.message);
        }
    }

    /**
     * Check for velocity/thrust entry signals and execute trades
     * @param {number} timestamp - Current timestamp
     */
    async checkForVelocitySignals(timestamp) {
        // Detect velocity and thrust signals
        const signals = this.velocityDetector.detectEntrySignals(timestamp);
        
        if (signals.length === 0) {
            return; // No signals detected
        }

        // Sort signals by confidence and type preference
        signals.sort((a, b) => {
            // Prefer velocity signals over thrust signals
            if (a.type !== b.type) {
                return a.type === 'VELOCITY' ? -1 : 1;
            }
            // Then by confidence
            return b.confidence - a.confidence;
        });

        // Take the best signal
        const bestSignal = signals[0];
        
        // Validate signal before execution
        if (this.validateVelocitySignal(bestSignal)) {
            await this.executeVelocityEntry(bestSignal, timestamp);
        } else {
            this.missedSignals++;
            console.log(`❌ Signal validation failed for ${bestSignal.symbol} (${bestSignal.type})`);
        }
    }

    /**
     * Validate a velocity/thrust signal before execution
     * @param {Object} signal - Signal to validate
     * @returns {boolean} True if signal is valid
     */
    validateVelocitySignal(signal) {
        // Check minimum confidence
        if (signal.confidence < this.minConfidence) {
            return false;
        }

        // Check if coin is still in acceptable rank
        if (signal.rank > this.maxRank) {
            return false;
        }

        // Check if we have sufficient balance
        if (!this.tradingEngine.canTrade()) {
            return false;
        }

        // Velocity-specific validation
        if (signal.type === 'VELOCITY') {
            // Ensure velocity is still above threshold
            if (signal.velocity < this.velocityDetector.VELOCITY_THRESHOLD) {
                return false;
            }
        }

        // Thrust-specific validation
        if (signal.type === 'THRUST') {
            // Ensure thrust is still above threshold
            if (signal.thrust < this.velocityDetector.THRUST_THRESHOLD) {
                return false;
            }
        }

        return true;
    }

    /**
     * Execute velocity/thrust entry trade
     * @param {Object} signal - Entry signal
     * @param {number} timestamp - Execution timestamp
     */
    async executeVelocityEntry(signal, timestamp) {
        console.log(`\n⚡ VELOCITY ${signal.type} ENTRY: ${signal.symbol}`);
        console.log(`   Confidence: ${signal.confidence.toFixed(1)}%`);
        console.log(`   Rank: #${signal.rank}`);
        console.log(`   Entry Price: $${signal.entryPrice.toFixed(6)}`);

        if (signal.type === 'VELOCITY') {
            console.log(`   🚀 3s Velocity: ${signal.velocityPercent.toFixed(3)}% (≥0.35% ✅)`);
            console.log(`   📈 Pressure: ${signal.pressurePercent.toFixed(1)}% (≥15% ✅)`);
        } else if (signal.type === 'THRUST') {
            console.log(`   🚀 5s Thrust: ${signal.thrustPercent.toFixed(3)}%`);
        }

        console.log(`   🎯 Simple Exit Strategy: 100% at 1.20% profit target`);
        console.log(`   📊 8EMA Filter: Active ✅`);
        console.log(`   📋 Spread Filter: ≤0.13% ✅`);

        const result = this.tradingEngine.enterPosition(
            signal.symbol,
            signal.entryPrice,
            timestamp
        );

        if (result.success) {
            this.executedTrades++;
            
            // Track signal type
            if (signal.type === 'VELOCITY') {
                this.velocityTrades++;
            } else {
                this.thrustTrades++;
            }
            
            console.log(`✅ ${signal.type} position entered successfully`);
            
            // Start monitoring the position
            this.startPositionMonitoring(signal);
        } else {
            this.missedSignals++;
            console.log(`❌ Failed to enter ${signal.type} position: ${result.message}`);
        }
    }

    /**
     * Start monitoring a specific position
     * @param {Object} entrySignal - Original entry signal
     */
    startPositionMonitoring(entrySignal) {
        console.log(`👁️ Starting position monitoring for ${entrySignal.symbol} (${entrySignal.type})`);
        
        // Store entry signal data for reference
        if (this.tradingEngine.currentPosition) {
            this.tradingEngine.currentPosition.entrySignal = entrySignal;
            this.tradingEngine.currentPosition.entryType = entrySignal.type;
        }
    }

    /**
     * Monitor current position for exit conditions
     * @param {number} timestamp - Current timestamp
     */
    async monitorCurrentPosition(timestamp) {
        const position = this.tradingEngine.currentPosition;
        if (!position) {
            return;
        }

        // Get real-time current price (more accurate for stop loss)
        const currentPrice = this.getRealTimePrice(position.symbol);
        if (!currentPrice) {
            return;
        }

        // CRITICAL SAFETY CHECK: Always respect stop loss immediately
        if (currentPrice <= position.stopLossPrice) {
            console.log(`🚨 STOP LOSS TRIGGERED: ${position.symbol}`);
            console.log(`   Current: $${currentPrice.toFixed(6)} <= Stop: $${position.stopLossPrice.toFixed(6)}`);

            // CRITICAL FIX: Never exit worse than stop loss + small slippage buffer
            const maxSlippage = 0.001; // 0.1% maximum slippage
            const worstExitPrice = position.stopLossPrice * (1 - maxSlippage);
            const exitPrice = Math.max(currentPrice, worstExitPrice);

            console.log(`   Protected Exit: $${exitPrice.toFixed(6)} (max slippage: 0.1%)`);

            await this.executeExit({
                type: 'STOP_LOSS',
                reason: 'Stop loss triggered',
                price: exitPrice
            }, timestamp);
            return;
        }

        // Update position with current price
        const updateResult = this.tradingEngine.updatePosition(currentPrice, timestamp);
        if (!updateResult.success) {
            return;
        }

        // Check for exit conditions from paper trading engine
        const exitCondition = updateResult.exitCondition;
        if (exitCondition) {
            await this.executeExit(exitCondition, timestamp);
            return;
        }

        // Check additional exit conditions (these should never override stop loss)
        const additionalExit = this.checkAdditionalExitConditions(position, timestamp);
        if (additionalExit) {
            await this.executeExit(additionalExit, timestamp);
        }
    }

    /**
     * Get current price from velocity detector's price history
     * @param {string} symbol - Symbol to get price for
     * @returns {number|null} Current price or null
     */
    getCurrentPriceFromHistory(symbol) {
        const priceHistory = this.velocityDetector.priceHistory.get(symbol);
        if (!priceHistory || priceHistory.length === 0) {
            return null;
        }

        // Get the most recent price
        return priceHistory[priceHistory.length - 1].price;
    }

    /**
     * Get real-time current price directly from market data
     * @param {string} symbol - Symbol to get price for
     * @returns {number|null} Current price or null
     */
    getRealTimePrice(symbol) {
        // Try to get from current tickers (most recent market data)
        if (this.currentTickers && this.currentTickers.length > 0) {
            const ticker = this.currentTickers.find(t => t.symbol === symbol);
            if (ticker && ticker.last) {
                return parseFloat(ticker.last);
            }
        }

        // Fallback to price history
        return this.getCurrentPriceFromHistory(symbol);
    }

    /**
     * Check additional exit conditions beyond basic profit/loss
     * @param {Object} position - Current position
     * @param {number} timestamp - Current timestamp
     * @returns {Object|null} Exit condition or null
     */
    checkAdditionalExitConditions(position, timestamp) {
        const currentPrice = position.lastPrice;

        // CRITICAL: Never override stop loss! If we're below stop loss, that takes priority
        if (currentPrice <= position.stopLossPrice) {
            return {
                type: 'STOP_LOSS',
                reason: 'Stop loss triggered',
                price: currentPrice
            };
        }

        // Smart Exit Logic: Fee Protection + Maximum Hold Time
        const holdTime = timestamp - position.entryTime;
        const feeProtectionThreshold = position.entryPrice * 1.002; // 0.2% above entry (covers fees)

        // After 5 minutes: Exit if we have at least 0.2% profit (fee protection)
        if (holdTime > this.exitConditions.maxHoldTime && currentPrice >= feeProtectionThreshold) {
            return {
                type: 'FEE_PROTECTION_EXIT',
                reason: 'Fee protection exit (0.2% minimum profit)',
                price: currentPrice
            };
        }

        // After 10 minutes: Force exit regardless (absolute maximum)
        if (holdTime > (this.exitConditions.maxHoldTime * 2)) {
            // If we're at a loss, exit at stop loss price, not current price
            const exitPrice = currentPrice < position.entryPrice ?
                Math.max(currentPrice, position.stopLossPrice) : currentPrice;

            return {
                type: 'ABSOLUTE_MAX_TIME',
                reason: 'Absolute maximum hold time exceeded',
                price: exitPrice
            };
        }

        // Check if coin dropped out of top gainers (but respect stop loss)
        const currentRank = this.getCurrentGainerRank(position.symbol);
        if (currentRank > this.exitConditions.gainerRankExit) {
            // If we're at a loss, exit at stop loss price, not current price
            const exitPrice = currentPrice < position.entryPrice ?
                Math.max(currentPrice, position.stopLossPrice) : currentPrice;

            return {
                type: 'GAINER_RANK_EXIT',
                reason: `Dropped to rank #${currentRank}`,
                price: exitPrice
            };
        }

        return null;
    }

    /**
     * Get current gainer rank for a symbol
     * @param {string} symbol - Symbol to check
     * @returns {number} Current rank (16+ if not in top 15)
     */
    getCurrentGainerRank(symbol) {
        return this.velocityDetector.gainerRanks.get(symbol) || 16;
    }

    /**
     * Execute exit trade
     * @param {Object} exitCondition - Exit condition details
     * @param {number} timestamp - Exit timestamp
     */
    async executeExit(exitCondition, timestamp) {
        const position = this.tradingEngine.currentPosition;
        if (!position) {
            return;
        }

        console.log(`\n🏁 EXECUTING EXIT: ${position.symbol} (${position.entryType || 'VELOCITY'})`);
        console.log(`   Reason: ${exitCondition.reason}`);
        console.log(`   Exit Price: $${exitCondition.price.toFixed(6)}`);

        const result = this.tradingEngine.exitPosition(
            exitCondition.price,
            exitCondition.reason,
            timestamp
        );

        if (result.success) {
            console.log(`✅ Position exited successfully`);
            this.logTradePerformance(result.trade);

            // Add coin to cooldown after any trade (win or loss)
            this.velocityDetector.addToCooldown(position.symbol, timestamp);

            // Update signal accuracy
            this.updateSignalAccuracy(result.trade);
        } else {
            console.log(`❌ Failed to exit position: ${result.message}`);
        }
    }

    /**
     * Update signal accuracy tracking
     * @param {Object} trade - Completed trade
     */
    updateSignalAccuracy(trade) {
        const isWin = trade.netProfit > 0;
        
        // Track accuracy by signal type
        if (trade.entrySignal && trade.entrySignal.type) {
            const signalType = trade.entrySignal.type;
            console.log(`📊 ${signalType} signal result: ${isWin ? 'WIN' : 'LOSS'}`);
        }
        
        // Calculate overall accuracy
        const stats = this.tradingEngine.getStatistics();
        this.signalAccuracy = stats.totalTrades > 0 ? (stats.winningTrades / stats.totalTrades) * 100 : 0;
    }

    /**
     * Log trade performance details
     * @param {Object} trade - Completed trade
     */
    logTradePerformance(trade) {
        const isWin = trade.netProfit > 0;
        const stats = this.tradingEngine.getStatistics();
        
        console.log(`\n📊 VELOCITY TRADE PERFORMANCE:`);
        console.log(`   Result: ${isWin ? '✅ WIN' : '❌ LOSS'}`);
        console.log(`   P&L: ${trade.netProfit > 0 ? '+' : ''}$${trade.netProfit.toFixed(4)}`);
        console.log(`   Duration: ${Math.round(trade.holdingTime / 1000)}s`);
        console.log(`   Signal Type: ${trade.entrySignal?.type || 'UNKNOWN'}`);
        console.log(`   Win Rate: ${stats.winRate.toFixed(1)}%`);
        console.log(`   Balance: $${stats.currentBalance.toFixed(2)}`);
        console.log(`   Daily P&L: ${stats.dailyProfit > 0 ? '+' : ''}$${stats.dailyProfit.toFixed(2)}`);
        console.log(`   Velocity Trades: ${this.velocityTrades} | Thrust Trades: ${this.thrustTrades}`);
    }

    /**
     * Update with real-time market data
     * @param {Array} topGainers - Current top gainers
     * @param {Array} tickers - Current ticker data
     * @param {number} timestamp - Data timestamp
     * @param {Map} orderbooks - Orderbook data for symbols
     */
    updateMarketData(topGainers, tickers, timestamp = Date.now(), orderbooks = null) {
        // Store current tickers for real-time price access
        this.currentTickers = tickers;

        // Update velocity detector with new data
        this.velocityDetector.updateMarketData(tickers, topGainers, timestamp, orderbooks);
    }

    /**
     * Get position manager statistics
     * @returns {Object} Statistics object
     */
    getStatistics() {
        const tradingStats = this.tradingEngine.getStatistics();
        const velocityStats = this.velocityDetector.getStatistics();
        
        return {
            ...tradingStats,
            executedTrades: this.executedTrades,
            missedSignals: this.missedSignals,
            velocityTrades: this.velocityTrades,
            thrustTrades: this.thrustTrades,
            signalAccuracy: this.signalAccuracy,
            isMonitoring: this.isMonitoring,
            ...velocityStats
        };
    }

    /**
     * Get current position details
     * @returns {Object|null} Current position or null
     */
    getCurrentPosition() {
        return this.tradingEngine.currentPosition;
    }

    /**
     * Get momentum data for dashboard
     * @returns {Array} Array of momentum data
     */
    getMomentumData() {
        return this.velocityDetector.getMomentumData();
    }

    /**
     * Force exit current position (emergency exit)
     * @param {string} reason - Reason for forced exit
     */
    async forceExit(reason = 'Manual exit') {
        if (!this.tradingEngine.isInTrade) {
            return { success: false, message: 'No active position to exit' };
        }

        const position = this.tradingEngine.currentPosition;
        const currentPrice = this.getRealTimePrice(position.symbol);
        
        if (!currentPrice) {
            return { success: false, message: 'Unable to get current price' };
        }

        return await this.executeExit({
            type: 'MANUAL',
            reason: reason,
            price: currentPrice
        }, Date.now());
    }
}

module.exports = VelocityPositionManager;
