/**
 * Position Manager
 * Manages trade execution, monitoring, and exit conditions
 * Integrates with PaperTradingEngine and EntrySignalDetector
 */
class PositionManager {
    constructor(tradingEngine, signalDetector) {
        this.tradingEngine = tradingEngine;
        this.signalDetector = signalDetector;
        
        // Position monitoring
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.lastPriceUpdate = 0;
        
        // Exit conditions tracking
        this.exitConditions = {
            profitTarget: 0.0056, // 0.56%
            stopLoss: 0.0015, // 0.15%
            maxRedTicks: 3, // Exit after 3 consecutive red ticks
            maxHoldTime: 300000, // 5 minutes max hold time
            gainerRankExit: 10 // Exit if coin drops out of top 10
        };
        
        // Performance tracking
        this.executedTrades = 0;
        this.missedSignals = 0;
        this.falseSignals = 0;
        
        console.log('⚡ Position Manager initialized');
        console.log(`🎯 Profit Target: ${(this.exitConditions.profitTarget * 100).toFixed(2)}%`);
        console.log(`🛑 Stop Loss: ${(this.exitConditions.stopLoss * 100).toFixed(2)}%`);
    }

    /**
     * Start position monitoring and signal detection
     * @param {number} updateInterval - Update interval in milliseconds
     */
    startMonitoring(updateInterval = 1000) {
        if (this.isMonitoring) {
            console.log('⚠️ Position monitoring already active');
            return;
        }

        this.isMonitoring = true;
        console.log('🔄 Starting position monitoring...');
        
        this.monitoringInterval = setInterval(() => {
            this.monitoringCycle();
        }, updateInterval);
    }

    /**
     * Stop position monitoring
     */
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }

        this.isMonitoring = false;
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        
        console.log('⏹️ Position monitoring stopped');
    }

    /**
     * Main monitoring cycle - called every update interval
     */
    async monitoringCycle() {
        try {
            const timestamp = Date.now();
            
            // Check for new entry signals if not in trade
            if (!this.tradingEngine.isInTrade) {
                await this.checkForEntrySignals(timestamp);
            } else {
                // Monitor current position
                await this.monitorCurrentPosition(timestamp);
            }
            
            this.lastPriceUpdate = timestamp;
            
        } catch (error) {
            console.error('❌ Error in monitoring cycle:', error.message);
        }
    }

    /**
     * Check for entry signals and execute trades
     * @param {number} timestamp - Current timestamp
     */
    async checkForEntrySignals(timestamp) {
        // Detect entry signals
        const signals = this.signalDetector.detectEntrySignals(timestamp);
        
        if (signals.length === 0) {
            return; // No signals detected
        }

        // Sort signals by confidence and rank
        signals.sort((a, b) => {
            if (a.confidence !== b.confidence) {
                return b.confidence - a.confidence; // Higher confidence first
            }
            return a.gainerRank - b.gainerRank; // Lower rank (better position) first
        });

        // Take the best signal
        const bestSignal = signals[0];
        
        // Validate signal before execution
        if (this.validateSignal(bestSignal)) {
            await this.executeEntry(bestSignal, timestamp);
        } else {
            this.falseSignals++;
            console.log(`❌ Signal validation failed for ${bestSignal.symbol}`);
        }
    }

    /**
     * Validate a signal before execution
     * @param {Object} signal - Signal to validate
     * @returns {boolean} True if signal is valid
     */
    validateSignal(signal) {
        // Check minimum confidence
        if (signal.confidence < 60) {
            return false;
        }

        // Check if coin is still in top gainers
        if (signal.gainerRank > 7) {
            return false;
        }

        // Check if we have sufficient balance
        if (!this.tradingEngine.canTrade()) {
            return false;
        }

        // Additional validation can be added here
        return true;
    }

    /**
     * Execute entry trade
     * @param {Object} signal - Entry signal
     * @param {number} timestamp - Execution timestamp
     */
    async executeEntry(signal, timestamp) {
        console.log(`🚀 EXECUTING ENTRY: ${signal.symbol}`);
        console.log(`   Confidence: ${signal.confidence.toFixed(1)}%`);
        console.log(`   Rank: #${signal.gainerRank}`);
        console.log(`   Entry Price: $${signal.entryPrice.toFixed(6)}`);

        const result = this.tradingEngine.enterPosition(
            signal.symbol,
            signal.entryPrice,
            timestamp
        );

        if (result.success) {
            this.executedTrades++;
            console.log(`✅ Position entered successfully`);
            
            // Start monitoring the position
            this.startPositionMonitoring(signal);
        } else {
            this.missedSignals++;
            console.log(`❌ Failed to enter position: ${result.message}`);
        }
    }

    /**
     * Start monitoring a specific position
     * @param {Object} entrySignal - Original entry signal
     */
    startPositionMonitoring(entrySignal) {
        console.log(`👁️ Starting position monitoring for ${entrySignal.symbol}`);
        
        // Store entry signal data for reference
        if (this.tradingEngine.currentPosition) {
            this.tradingEngine.currentPosition.entrySignal = entrySignal;
        }
    }

    /**
     * Monitor current position for exit conditions
     * @param {number} timestamp - Current timestamp
     */
    async monitorCurrentPosition(timestamp) {
        const position = this.tradingEngine.currentPosition;
        if (!position) {
            return;
        }

        // Get current price (this would come from real-time data in production)
        const currentPrice = await this.getCurrentPrice(position.symbol);
        if (!currentPrice) {
            return;
        }

        // Update position with current price
        const updateResult = this.tradingEngine.updatePosition(currentPrice, timestamp);
        if (!updateResult.success) {
            return;
        }

        // Check for exit conditions
        const exitCondition = updateResult.exitCondition;
        if (exitCondition) {
            await this.executeExit(exitCondition, timestamp);
            return;
        }

        // Check additional exit conditions
        const additionalExit = this.checkAdditionalExitConditions(position, timestamp);
        if (additionalExit) {
            await this.executeExit(additionalExit, timestamp);
        }
    }

    /**
     * Check additional exit conditions beyond basic profit/loss
     * @param {Object} position - Current position
     * @param {number} timestamp - Current timestamp
     * @returns {Object|null} Exit condition or null
     */
    checkAdditionalExitConditions(position, timestamp) {
        // Check maximum hold time
        const holdTime = timestamp - position.entryTime;
        if (holdTime > this.exitConditions.maxHoldTime) {
            return {
                type: 'MAX_HOLD_TIME',
                reason: 'Maximum hold time exceeded',
                price: position.lastPrice
            };
        }

        // Check if coin dropped out of top gainers
        const currentRank = this.getCurrentGainerRank(position.symbol);
        if (currentRank > this.exitConditions.gainerRankExit) {
            return {
                type: 'GAINER_RANK_EXIT',
                reason: `Dropped to rank #${currentRank}`,
                price: position.lastPrice
            };
        }

        return null;
    }

    /**
     * Execute exit trade
     * @param {Object} exitCondition - Exit condition details
     * @param {number} timestamp - Exit timestamp
     */
    async executeExit(exitCondition, timestamp) {
        const position = this.tradingEngine.currentPosition;
        if (!position) {
            return;
        }

        console.log(`🏁 EXECUTING EXIT: ${position.symbol}`);
        console.log(`   Reason: ${exitCondition.reason}`);
        console.log(`   Exit Price: $${exitCondition.price.toFixed(6)}`);

        const result = this.tradingEngine.exitPosition(
            exitCondition.price,
            exitCondition.reason,
            timestamp
        );

        if (result.success) {
            console.log(`✅ Position exited successfully`);
            this.logTradePerformance(result.trade);
        } else {
            console.log(`❌ Failed to exit position: ${result.message}`);
        }
    }

    /**
     * Log trade performance details
     * @param {Object} trade - Completed trade
     */
    logTradePerformance(trade) {
        const isWin = trade.netProfit > 0;
        const stats = this.tradingEngine.getStatistics();
        
        console.log(`\n📊 TRADE PERFORMANCE:`);
        console.log(`   Result: ${isWin ? '✅ WIN' : '❌ LOSS'}`);
        console.log(`   P&L: ${trade.netProfit > 0 ? '+' : ''}$${trade.netProfit.toFixed(4)}`);
        console.log(`   Duration: ${Math.round(trade.holdingTime / 1000)}s`);
        console.log(`   Win Rate: ${stats.winRate.toFixed(1)}%`);
        console.log(`   Balance: $${stats.currentBalance.toFixed(2)}`);
        console.log(`   Daily P&L: ${stats.dailyProfit > 0 ? '+' : ''}$${stats.dailyProfit.toFixed(2)}`);
    }

    /**
     * Get current price for a symbol (placeholder - would integrate with real data)
     * @param {string} symbol - Symbol to get price for
     * @returns {number|null} Current price or null
     */
    async getCurrentPrice(symbol) {
        // This is a placeholder - in production this would fetch real-time price
        // For now, we'll simulate price movement
        const position = this.tradingEngine.currentPosition;
        if (!position) return null;

        // Simulate realistic price movement (±0.1% random walk)
        const lastPrice = position.lastPrice;
        const randomChange = (Math.random() - 0.5) * 0.002; // ±0.1%
        const newPrice = lastPrice * (1 + randomChange);
        
        return newPrice;
    }

    /**
     * Get current gainer rank for a symbol
     * @param {string} symbol - Symbol to check
     * @returns {number} Current rank (11+ if not in top 10)
     */
    getCurrentGainerRank(symbol) {
        // This would integrate with real gainer data
        // For now, simulate rank changes
        const gainerData = this.signalDetector.gainerHistory.get(symbol);
        if (!gainerData) return 11;
        
        // Simulate occasional rank drops
        if (Math.random() < 0.1) { // 10% chance of rank drop
            return gainerData.rank + Math.floor(Math.random() * 3) + 1;
        }
        
        return gainerData.rank;
    }

    /**
     * Update with real-time market data
     * @param {Array} topGainers - Current top gainers
     * @param {Array} tickers - Current ticker data
     * @param {number} timestamp - Data timestamp
     */
    updateMarketData(topGainers, tickers, timestamp = Date.now()) {
        // Update signal detector with new data
        this.signalDetector.updateGainerTracking(topGainers, timestamp);
        this.signalDetector.updatePriceHistory(tickers, timestamp);
    }

    /**
     * Get position manager statistics
     * @returns {Object} Statistics object
     */
    getStatistics() {
        const tradingStats = this.tradingEngine.getStatistics();
        const signalStats = this.signalDetector.getStatistics();
        
        return {
            ...tradingStats,
            executedTrades: this.executedTrades,
            missedSignals: this.missedSignals,
            falseSignals: this.falseSignals,
            signalAccuracy: this.executedTrades > 0 ? 
                ((this.executedTrades - this.falseSignals) / this.executedTrades) * 100 : 0,
            isMonitoring: this.isMonitoring,
            qualifiedGainers: signalStats.qualifiedGainers,
            ...signalStats
        };
    }

    /**
     * Get current position details
     * @returns {Object|null} Current position or null
     */
    getCurrentPosition() {
        return this.tradingEngine.currentPosition;
    }

    /**
     * Get qualified gainers for monitoring
     * @returns {Array} Array of qualified gainers
     */
    getQualifiedGainers() {
        return this.signalDetector.getQualifiedGainers();
    }

    /**
     * Force exit current position (emergency exit)
     * @param {string} reason - Reason for forced exit
     */
    async forceExit(reason = 'Manual exit') {
        if (!this.tradingEngine.isInTrade) {
            return { success: false, message: 'No active position to exit' };
        }

        const position = this.tradingEngine.currentPosition;
        const currentPrice = await this.getCurrentPrice(position.symbol);
        
        if (!currentPrice) {
            return { success: false, message: 'Unable to get current price' };
        }

        return await this.executeExit({
            type: 'MANUAL',
            reason: reason,
            price: currentPrice
        }, Date.now());
    }
}

module.exports = PositionManager;
