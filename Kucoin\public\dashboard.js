// Dashboard JavaScript
class KuCoinDashboard {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.autoRefresh = true;
        this.currentGainers = [];
        this.lastUpdate = null;
        
        this.init();
    }

    init() {
        this.connectWebSocket();
        this.setupEventListeners();
        this.updateConnectionStatus(false);
    }

    connectWebSocket() {
        this.socket = io();

        this.socket.on('connect', () => {
            this.isConnected = true;
            this.updateConnectionStatus(true);
            this.showToast('Connected to server', 'success');
        });

        this.socket.on('disconnect', () => {
            this.isConnected = false;
            this.updateConnectionStatus(false);
            this.showToast('Disconnected from server', 'error');
        });

        this.socket.on('gainers-update', (data) => {
            this.updateGainersTable(data.gainers);
            this.updateStats(data);
            this.lastUpdate = data.lastUpdate;
            this.updateLastUpdateTime();
        });

        this.socket.on('prices-update', (data) => {
            this.updatePrices(data.gainers);
            this.lastUpdate = data.lastUpdate;
            this.updateLastUpdateTime();
        });

        this.socket.on('status-update', (data) => {
            this.updateStatusInfo(data);
        });
    }

    setupEventListeners() {
        // Auto-refresh toggle
        window.toggleAutoRefresh = () => {
            this.autoRefresh = !this.autoRefresh;
            const icon = document.getElementById('autoRefreshIcon');
            const text = document.getElementById('autoRefreshText');
            
            if (this.autoRefresh) {
                icon.className = 'fas fa-play';
                text.textContent = 'Auto: ON';
            } else {
                icon.className = 'fas fa-pause';
                text.textContent = 'Auto: OFF';
            }
        };

        // Manual refresh
        window.refreshData = () => {
            if (this.socket && this.isConnected) {
                this.socket.emit('request-refresh');
                this.showToast('Refreshing data...', 'info');
            }
        };

        // Update time every second
        setInterval(() => {
            this.updateLastUpdateTime();
        }, 1000);
    }

    updateConnectionStatus(connected) {
        const indicator = document.getElementById('connectionStatus');
        const text = document.getElementById('connectionText');
        
        if (connected) {
            indicator.classList.add('connected');
            text.textContent = 'Connected';
        } else {
            indicator.classList.remove('connected');
            text.textContent = 'Disconnected';
        }
    }

    updateGainersTable(gainers) {
        this.currentGainers = gainers;
        const tbody = document.getElementById('gainersTableBody');
        
        if (!gainers || gainers.length === 0) {
            tbody.innerHTML = `
                <tr class="loading-row">
                    <td colspan="7">
                        <div class="loading-spinner">
                            <i class="fas fa-exclamation-triangle"></i>
                            No gainers data available
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = gainers.map((gainer, index) => {
            const changeClass = gainer.changeRatePercent > 0 ? 'change-positive' : 'change-negative';
            const changeIcon = gainer.changeRatePercent > 0 ? 'fa-arrow-up' : 'fa-arrow-down';
            
            return `
                <tr data-symbol="${gainer.symbol}">
                    <td class="rank">${index + 1}</td>
                    <td class="symbol">
                        <strong>${gainer.symbol}</strong>
                        <br>
                        <small style="opacity: 0.7">${gainer.baseCurrency}</small>
                    </td>
                    <td class="price">$${gainer.priceFormatted}</td>
                    <td class="${changeClass}">
                        <i class="fas ${changeIcon}"></i>
                        ${gainer.changePercentFormatted}
                        <br>
                        <small>$${gainer.changePrice > 0 ? '+' : ''}${gainer.changePrice.toFixed(6)}</small>
                    </td>
                    <td class="price-change-30min">
                        ${gainer.priceChange30min ? `+${gainer.priceChange30min.toFixed(2)}%` : 'N/A'}
                    </td>
                    <td class="high-low">
                        <div>H: $${gainer.high24h.toFixed(6)}</div>
                        <div>L: $${gainer.low24h.toFixed(6)}</div>
                    </td>
                    <td class="last-update">${this.formatTimeAgo(gainer.lastUpdate)}</td>
                </tr>
            `;
        }).join('');
    }

    updatePrices(gainers) {
        gainers.forEach((gainer, index) => {
            const row = document.querySelector(`tr[data-symbol="${gainer.symbol}"]`);
            if (row) {
                const priceCell = row.querySelector('.price');
                const changeCell = row.querySelector('.change-positive, .change-negative');
                
                if (priceCell) {
                    priceCell.textContent = `$${gainer.priceFormatted}`;
                    priceCell.classList.add('price-update');
                    setTimeout(() => priceCell.classList.remove('price-update'), 500);
                }
                
                if (changeCell) {
                    const changeClass = gainer.changeRatePercent > 0 ? 'change-positive' : 'change-negative';
                    const changeIcon = gainer.changeRatePercent > 0 ? 'fa-arrow-up' : 'fa-arrow-down';
                    
                    changeCell.className = changeClass;
                    changeCell.innerHTML = `
                        <i class="fas ${changeIcon}"></i>
                        ${gainer.changePercentFormatted}
                        <br>
                        <small>$${gainer.changePrice > 0 ? '+' : ''}${gainer.changePrice.toFixed(6)}</small>
                    `;
                }
            }
        });
    }

    updateStats(data) {
        const gainers = data.gainers || [];
        
        // Top gainer percentage
        const topGainerPercent = gainers.length > 0 ? gainers[0].changePercentFormatted : '0%';
        document.getElementById('topGainerPercent').textContent = topGainerPercent;
        
        // Total gainers
        document.getElementById('totalGainers').textContent = gainers.length;
        
        // Update count
        document.getElementById('updateCount').textContent = data.updateCount || 0;
        
        // Average 30min momentum
        const avgMomentum = gainers.length > 0
            ? gainers.reduce((sum, gainer) => sum + (gainer.priceChange30min || 0), 0) / gainers.length
            : 0;
        document.getElementById('totalVolume').textContent = `+${avgMomentum.toFixed(2)}%`;
    }

    updateStatusInfo(data) {
        document.getElementById('clientCount').textContent = data.connectedClients || 0;
        document.getElementById('updateCount').textContent = data.updateCount || 0;
    }

    updateLastUpdateTime() {
        if (this.lastUpdate) {
            const timeAgo = this.formatTimeAgo(this.lastUpdate);
            document.getElementById('lastUpdate').textContent = timeAgo;
        }
    }

    formatTimeAgo(timestamp) {
        if (!timestamp) return 'Never';
        
        const now = new Date();
        const updateTime = new Date(timestamp);
        const diffMs = now - updateTime;
        const diffSecs = Math.floor(diffMs / 1000);
        
        if (diffSecs < 60) {
            return `${diffSecs}s ago`;
        } else if (diffSecs < 3600) {
            return `${Math.floor(diffSecs / 60)}m ago`;
        } else {
            return updateTime.toLocaleTimeString();
        }
    }

    formatVolume(volume) {
        if (volume >= 1000000000) {
            return `$${(volume / 1000000000).toFixed(2)}B`;
        } else if (volume >= 1000000) {
            return `$${(volume / 1000000).toFixed(2)}M`;
        } else if (volume >= 1000) {
            return `$${(volume / 1000).toFixed(2)}K`;
        } else {
            return `$${volume.toFixed(2)}`;
        }
    }

    showToast(message, type = 'info') {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}"></i>
            ${message}
        `;
        
        container.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new KuCoinDashboard();
});

// Global function for strategy switching
async function switchStrategy(strategy) {
    try {
        const response = await fetch('/api/top-gainers-strategy', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ strategy })
        });

        const result = await response.json();

        if (result.success) {
            // Update button states
            document.querySelectorAll('.btn-strategy').forEach(btn => {
                btn.classList.remove('active');
            });

            if (strategy === 'PRICE_24H') {
                document.getElementById('strategy24h').classList.add('active');
            } else {
                document.getElementById('strategy30min').classList.add('active');
            }

            window.dashboard.showToast(`Strategy switched to ${strategy === 'PRICE_24H' ? '24h Price (Proven)' : '30min Price (Experimental)'}`, 'success');

            // Refresh data to show new strategy results
            window.dashboard.refreshData();
        } else {
            window.dashboard.showToast('Failed to switch strategy', 'error');
        }
    } catch (error) {
        console.error('Error switching strategy:', error);
        window.dashboard.showToast('Error switching strategy', 'error');
    }
}
