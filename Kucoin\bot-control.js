#!/usr/bin/env node

/**
 * Bot Control Script
 * Interactive control for the velocity trading bot
 * Allows strategy switching and bot reset
 */

const readline = require('readline');

class BotController {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        this.botProcess = null;
        this.currentStrategy = 'VELOCITY';
        
        console.log('🎮 KuCoin Velocity Trading Bot Controller');
        console.log('==========================================');
    }

    showMenu() {
        console.log('\n📋 AVAILABLE COMMANDS:');
        console.log('1. velocity    - Switch to VELOCITY strategy (3-sec ≥0.50%)');
        console.log('2. thrust      - Switch to THRUST strategy (5-sec ≥0.40%)');
        console.log('3. start       - Start the trading bot');
        console.log('4. stop        - Stop the trading bot');
        console.log('5. reset       - Reset bot (balance, logs, stats)');
        console.log('6. status      - Show current status');
        console.log('7. exit        - Exit controller');
        console.log('==========================================');
    }

    async start() {
        this.showMenu();
        this.promptUser();
    }

    promptUser() {
        this.rl.question('\n🎮 Enter command: ', (command) => {
            this.handleCommand(command.toLowerCase().trim());
        });
    }

    async handleCommand(command) {
        switch (command) {
            case '1':
            case 'velocity':
                await this.setStrategy('VELOCITY');
                break;
            case '2':
            case 'thrust':
                await this.setStrategy('THRUST');
                break;
            case '3':
            case 'start':
                await this.startBot();
                break;
            case '4':
            case 'stop':
                await this.stopBot();
                break;
            case '5':
            case 'reset':
                await this.resetBot();
                break;
            case '6':
            case 'status':
                this.showStatus();
                break;
            case '7':
            case 'exit':
                this.exit();
                return;
            case 'help':
            case 'menu':
                this.showMenu();
                break;
            default:
                console.log('❌ Unknown command. Type "help" for available commands.');
        }

        this.promptUser();
    }

    async setStrategy(strategy) {
        console.log(`🔄 Switching to ${strategy} strategy...`);
        
        try {
            // Send HTTP request to bot to change strategy
            const response = await fetch('http://localhost:3002/api/strategy', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ strategy: strategy })
            });
            
            if (response.ok) {
                this.currentStrategy = strategy;
                console.log(`✅ Strategy changed to: ${strategy}`);
                console.log(`📊 Bot will now use ${strategy} signals only`);
                
                if (strategy === 'VELOCITY') {
                    console.log('🚀 Testing 3-second velocity strategy (≥0.50%) - Higher quality signals');
                } else if (strategy === 'THRUST') {
                    console.log('🎯 Testing 5-second thrust strategy (≥0.40%) - Higher quality signals');
                }
            } else {
                console.log('❌ Failed to change strategy. Is the bot running?');
            }
        } catch (error) {
            console.log('❌ Error connecting to bot. Make sure it\'s running on port 3002.');
        }
    }

    async startBot() {
        console.log('🚀 Starting trading bot...');

        try {
            const response = await fetch('http://localhost:3002/api/start', {
                method: 'POST'
            });

            if (response.ok) {
                console.log('✅ Bot started successfully!');
                console.log('⚡ Trading is now active');
            } else {
                console.log('❌ Failed to start bot. Is the bot server running?');
            }
        } catch (error) {
            console.log('❌ Error connecting to bot. Make sure it\'s running on port 3002.');
        }
    }

    async stopBot() {
        console.log('🛑 Stopping trading bot...');

        try {
            const response = await fetch('http://localhost:3002/api/stop', {
                method: 'POST'
            });

            if (response.ok) {
                console.log('✅ Bot stopped successfully!');
                console.log('⏸️ Trading is now paused');
                console.log('💰 Current balance and history preserved');
            } else {
                console.log('❌ Failed to stop bot. Is the bot running?');
            }
        } catch (error) {
            console.log('❌ Error connecting to bot. Make sure it\'s running on port 3002.');
        }
    }

    async resetBot() {
        console.log('🔄 Resetting trading bot...');

        try {
            const response = await fetch('http://localhost:3002/api/reset', {
                method: 'POST'
            });

            if (response.ok) {
                console.log('✅ Bot reset successfully!');
                console.log('💰 Balance restored to $100');
                console.log('📊 All trade logs cleared');
                console.log('📈 Statistics reset to zero');
            } else {
                console.log('❌ Failed to reset bot. Is the bot running?');
            }
        } catch (error) {
            console.log('❌ Error connecting to bot. Make sure it\'s running on port 3002.');
        }
    }

    async showStatus() {
        console.log('\n📊 CURRENT STATUS:');
        console.log(`🔄 Strategy: ${this.currentStrategy}`);
        console.log('🌐 Dashboard: http://localhost:3002/trading');
        console.log('📡 Bot should be running on port 3002');
        
        try {
            const response = await fetch('http://localhost:3002/api/status');
            if (response.ok) {
                const status = await response.json();
                console.log(`💰 Balance: $${status.balance?.toFixed(2) || 'N/A'}`);
                console.log(`📈 Total Trades: ${status.totalTrades || 0}`);
                console.log(`🎯 Win Rate: ${status.winRate?.toFixed(1) || 0}%`);
                console.log(`⚡ Signals: ${status.totalSignals || 0}`);
                console.log(`🔄 In Trade: ${status.isInTrade ? 'YES' : 'NO'}`);
            } else {
                console.log('❌ Could not fetch bot status');
            }
        } catch (error) {
            console.log('❌ Bot appears to be offline');
        }
    }

    exit() {
        console.log('\n👋 Exiting bot controller...');
        console.log('📊 Bot will continue running in background');
        console.log('🌐 Dashboard: http://localhost:3002/trading');
        this.rl.close();
        process.exit(0);
    }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n\n👋 Bot controller shutting down...');
    process.exit(0);
});

// Start the controller
if (require.main === module) {
    const controller = new BotController();
    controller.start();
}

module.exports = BotController;
