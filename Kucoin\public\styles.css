/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #ffffff;
    min-height: 100vh;
    line-height: 1.6;
}

/* Dashboard Container */
.dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.dashboard-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-section i {
    font-size: 2rem;
    color: #ffd700;
}

.logo-section h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.version {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-section {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.status-indicator {
    color: #ff4444;
    animation: pulse 2s infinite;
}

.status-indicator.connected {
    color: #00ff88;
    animation: none;
}

/* Main Content */
.dashboard-main {
    flex: 1;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1e3c72;
    font-size: 1.5rem;
}

.stat-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.2rem;
    color: #ffd700;
}

.stat-content p {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Controls Section */
.controls-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.controls-left h2 {
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.controls-left i {
    color: #ffd700;
}

.strategy-toggle {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.btn-strategy {
    padding: 8px 16px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-strategy:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.btn-strategy.active {
    background: #4CAF50;
    border-color: #4CAF50;
    color: white;
}

.controls-right {
    display: flex;
    gap: 1rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #00ff88, #00cc6a);
    color: #ffffff;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #00cc6a, #00aa55);
    transform: translateY(-2px);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Table Container */
.table-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    overflow: hidden;
    margin-bottom: 2rem;
}

.table-wrapper {
    overflow-x: auto;
}

.gainers-table {
    width: 100%;
    border-collapse: collapse;
}

.gainers-table th {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.gainers-table td {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
}

.gainers-table tbody tr {
    transition: background-color 0.3s ease;
}

.gainers-table tbody tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

.rank {
    font-weight: 700;
    color: #ffd700;
}

.symbol {
    font-weight: 600;
    color: #ffffff;
}

.price {
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

.change-positive {
    color: #00ff88;
    font-weight: 600;
}

.change-negative {
    color: #ff4444;
    font-weight: 600;
}

.volume {
    font-family: 'Courier New', monospace;
}

.high-low {
    font-size: 0.8rem;
    opacity: 0.8;
}

.last-update {
    font-size: 0.8rem;
    opacity: 0.7;
}

/* Loading */
.loading-row td {
    text-align: center;
    padding: 3rem;
}

.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    font-size: 1.1rem;
}

.loading-spinner i {
    font-size: 1.5rem;
    color: #ffd700;
}

/* Update Info */
.update-info {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.update-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    opacity: 0.8;
}

.update-item i {
    color: #ffd700;
}

/* Footer */
.dashboard-footer {
    background: rgba(0, 0, 0, 0.2);
    padding: 1.5rem 2rem;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
}

.footer-links {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    opacity: 0.7;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.toast {
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    border-left: 4px solid #ffd700;
    animation: slideIn 0.3s ease;
}

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Price Change Animation */
.price-update {
    animation: priceFlash 0.5s ease;
}

@keyframes priceFlash {
    0% { background-color: rgba(255, 215, 0, 0.3); }
    100% { background-color: transparent; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .dashboard-main {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .controls-section {
        flex-direction: column;
        align-items: stretch;
    }

    .controls-right {
        justify-content: center;
    }

    .update-info {
        flex-direction: column;
        gap: 1rem;
    }
}
