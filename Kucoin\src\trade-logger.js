const fs = require('fs');
const path = require('path');

/**
 * Trade Logger and Analytics
 * Comprehensive logging and analysis of trading performance
 */
class TradeLogger {
    constructor() {
        // File paths
        this.logsDir = path.join(__dirname, '..', 'logs');
        this.tradesFile = path.join(this.logsDir, 'trades.json');
        this.dailyStatsFile = path.join(this.logsDir, 'daily-stats.json');
        this.performanceFile = path.join(this.logsDir, 'performance.json');
        
        // In-memory data
        this.trades = [];
        this.dailyStats = {};
        this.performanceMetrics = {};
        
        // Analytics tracking
        this.sessionStart = Date.now();
        this.lastAnalysisTime = 0;
        
        this.initializeLogging();
        console.log('📝 Trade Logger initialized');
    }

    /**
     * Initialize logging system and create directories
     */
    initializeLogging() {
        // Create logs directory if it doesn't exist
        if (!fs.existsSync(this.logsDir)) {
            fs.mkdirSync(this.logsDir, { recursive: true });
        }

        // Load existing data
        this.loadExistingData();
    }

    /**
     * Load existing trade data from files
     */
    loadExistingData() {
        try {
            // Load trades
            if (fs.existsSync(this.tradesFile)) {
                const tradesData = fs.readFileSync(this.tradesFile, 'utf8');
                this.trades = JSON.parse(tradesData);
                console.log(`📊 Loaded ${this.trades.length} historical trades`);
            }

            // Load daily stats
            if (fs.existsSync(this.dailyStatsFile)) {
                const dailyData = fs.readFileSync(this.dailyStatsFile, 'utf8');
                this.dailyStats = JSON.parse(dailyData);
            }

            // Load performance metrics
            if (fs.existsSync(this.performanceFile)) {
                const perfData = fs.readFileSync(this.performanceFile, 'utf8');
                this.performanceMetrics = JSON.parse(perfData);
            }
        } catch (error) {
            console.error('⚠️ Error loading existing data:', error.message);
        }
    }

    /**
     * Log a completed trade
     * @param {Object} trade - Trade object from trading engine
     * @param {Object} signal - Original entry signal
     * @param {Object} marketConditions - Market conditions at time of trade
     */
    logTrade(trade, signal = null, marketConditions = null) {
        const enhancedTrade = {
            ...trade,
            signal: signal,
            marketConditions: marketConditions,
            sessionId: this.getSessionId(),
            loggedAt: Date.now()
        };

        // Add to in-memory array
        this.trades.push(enhancedTrade);

        // Update daily statistics
        this.updateDailyStats(enhancedTrade);

        // Save to file
        this.saveTradeToFile(enhancedTrade);

        // Update performance metrics
        this.updatePerformanceMetrics();

        // Log to console
        this.logTradeToConsole(enhancedTrade);

        console.log(`📝 Trade logged: ${trade.symbol} - ${trade.netProfit > 0 ? 'WIN' : 'LOSS'}`);
    }

    /**
     * Update daily statistics
     * @param {Object} trade - Trade object
     */
    updateDailyStats(trade) {
        const dateKey = new Date(trade.exitTime).toISOString().split('T')[0];
        
        if (!this.dailyStats[dateKey]) {
            this.dailyStats[dateKey] = {
                date: dateKey,
                totalTrades: 0,
                winningTrades: 0,
                losingTrades: 0,
                totalProfit: 0,
                totalLoss: 0,
                netProfit: 0,
                winRate: 0,
                avgWin: 0,
                avgLoss: 0,
                profitFactor: 0,
                maxWin: 0,
                maxLoss: 0,
                totalFees: 0,
                avgHoldTime: 0,
                symbols: new Set()
            };
        }

        const dayStats = this.dailyStats[dateKey];
        dayStats.totalTrades++;
        dayStats.symbols.add(trade.symbol);
        dayStats.totalFees += trade.totalFees;

        if (trade.netProfit > 0) {
            dayStats.winningTrades++;
            dayStats.totalProfit += trade.netProfit;
            dayStats.maxWin = Math.max(dayStats.maxWin, trade.netProfit);
        } else {
            dayStats.losingTrades++;
            dayStats.totalLoss += Math.abs(trade.netProfit);
            dayStats.maxLoss = Math.max(dayStats.maxLoss, Math.abs(trade.netProfit));
        }

        // Calculate derived metrics
        dayStats.netProfit = dayStats.totalProfit - dayStats.totalLoss;
        dayStats.winRate = (dayStats.winningTrades / dayStats.totalTrades) * 100;
        dayStats.avgWin = dayStats.winningTrades > 0 ? dayStats.totalProfit / dayStats.winningTrades : 0;
        dayStats.avgLoss = dayStats.losingTrades > 0 ? dayStats.totalLoss / dayStats.losingTrades : 0;
        dayStats.profitFactor = dayStats.totalLoss > 0 ? dayStats.totalProfit / dayStats.totalLoss : 0;
        
        // Calculate average hold time
        const dayTrades = this.trades.filter(t => 
            new Date(t.exitTime).toISOString().split('T')[0] === dateKey
        );
        dayStats.avgHoldTime = dayTrades.reduce((sum, t) => sum + t.holdingTime, 0) / dayTrades.length;

        // Convert Set to Array for JSON serialization
        dayStats.symbolsTraded = Array.from(dayStats.symbols);
        delete dayStats.symbols;

        // Save daily stats
        this.saveDailyStats();
    }

    /**
     * Update overall performance metrics
     */
    updatePerformanceMetrics() {
        if (this.trades.length === 0) return;

        const totalTrades = this.trades.length;
        const winningTrades = this.trades.filter(t => t.netProfit > 0);
        const losingTrades = this.trades.filter(t => t.netProfit <= 0);

        const totalProfit = winningTrades.reduce((sum, t) => sum + t.netProfit, 0);
        const totalLoss = losingTrades.reduce((sum, t) => sum + Math.abs(t.netProfit), 0);

        // Calculate streaks
        const streaks = this.calculateStreaks();
        
        // Calculate drawdown
        const drawdownData = this.calculateDrawdown();

        // Calculate by exit reason
        const exitReasons = this.analyzeExitReasons();

        // Calculate by time of day
        const timeAnalysis = this.analyzeTimePatterns();

        // Calculate by symbol performance
        const symbolAnalysis = this.analyzeSymbolPerformance();

        this.performanceMetrics = {
            overview: {
                totalTrades: totalTrades,
                winningTrades: winningTrades.length,
                losingTrades: losingTrades.length,
                winRate: (winningTrades.length / totalTrades) * 100,
                totalProfit: totalProfit,
                totalLoss: totalLoss,
                netProfit: totalProfit - totalLoss,
                profitFactor: totalLoss > 0 ? totalProfit / totalLoss : 0,
                avgWin: winningTrades.length > 0 ? totalProfit / winningTrades.length : 0,
                avgLoss: losingTrades.length > 0 ? totalLoss / losingTrades.length : 0,
                maxWin: Math.max(...winningTrades.map(t => t.netProfit), 0),
                maxLoss: Math.max(...losingTrades.map(t => Math.abs(t.netProfit)), 0),
                avgHoldTime: this.trades.reduce((sum, t) => sum + t.holdingTime, 0) / totalTrades,
                totalFees: this.trades.reduce((sum, t) => sum + t.totalFees, 0)
            },
            streaks: streaks,
            drawdown: drawdownData,
            exitReasons: exitReasons,
            timeAnalysis: timeAnalysis,
            symbolAnalysis: symbolAnalysis,
            lastUpdated: Date.now()
        };

        // Save performance metrics
        this.savePerformanceMetrics();
    }

    /**
     * Calculate winning and losing streaks
     * @returns {Object} Streak analysis
     */
    calculateStreaks() {
        if (this.trades.length === 0) return {};

        let currentStreak = 0;
        let maxWinStreak = 0;
        let maxLossStreak = 0;
        let currentWinStreak = 0;
        let currentLossStreak = 0;

        this.trades.forEach(trade => {
            if (trade.netProfit > 0) {
                currentWinStreak++;
                currentLossStreak = 0;
                maxWinStreak = Math.max(maxWinStreak, currentWinStreak);
            } else {
                currentLossStreak++;
                currentWinStreak = 0;
                maxLossStreak = Math.max(maxLossStreak, currentLossStreak);
            }
        });

        return {
            maxWinStreak: maxWinStreak,
            maxLossStreak: maxLossStreak,
            currentWinStreak: currentWinStreak,
            currentLossStreak: currentLossStreak
        };
    }

    /**
     * Calculate drawdown analysis
     * @returns {Object} Drawdown data
     */
    calculateDrawdown() {
        if (this.trades.length === 0) return {};

        let runningBalance = 100; // Starting balance
        let peak = 100;
        let maxDrawdown = 0;
        let currentDrawdown = 0;

        const balanceHistory = [{ balance: 100, timestamp: this.sessionStart }];

        this.trades.forEach(trade => {
            runningBalance = trade.balanceAfter;
            
            if (runningBalance > peak) {
                peak = runningBalance;
                currentDrawdown = 0;
            } else {
                currentDrawdown = ((peak - runningBalance) / peak) * 100;
                maxDrawdown = Math.max(maxDrawdown, currentDrawdown);
            }

            balanceHistory.push({
                balance: runningBalance,
                timestamp: trade.exitTime,
                drawdown: currentDrawdown
            });
        });

        return {
            maxDrawdown: maxDrawdown,
            currentDrawdown: currentDrawdown,
            peak: peak,
            balanceHistory: balanceHistory.slice(-100) // Keep last 100 points
        };
    }

    /**
     * Analyze exit reasons
     * @returns {Object} Exit reason analysis
     */
    analyzeExitReasons() {
        const reasons = {};
        
        this.trades.forEach(trade => {
            const reason = trade.exitReason;
            if (!reasons[reason]) {
                reasons[reason] = {
                    count: 0,
                    totalProfit: 0,
                    winRate: 0,
                    avgProfit: 0
                };
            }
            
            reasons[reason].count++;
            reasons[reason].totalProfit += trade.netProfit;
        });

        // Calculate derived metrics for each reason
        Object.keys(reasons).forEach(reason => {
            const data = reasons[reason];
            const reasonTrades = this.trades.filter(t => t.exitReason === reason);
            const wins = reasonTrades.filter(t => t.netProfit > 0).length;
            
            data.winRate = (wins / data.count) * 100;
            data.avgProfit = data.totalProfit / data.count;
        });

        return reasons;
    }

    /**
     * Analyze time-based patterns
     * @returns {Object} Time analysis
     */
    analyzeTimePatterns() {
        const hourlyStats = {};
        
        this.trades.forEach(trade => {
            const hour = new Date(trade.entryTime).getHours();
            
            if (!hourlyStats[hour]) {
                hourlyStats[hour] = {
                    trades: 0,
                    wins: 0,
                    totalProfit: 0
                };
            }
            
            hourlyStats[hour].trades++;
            if (trade.netProfit > 0) {
                hourlyStats[hour].wins++;
            }
            hourlyStats[hour].totalProfit += trade.netProfit;
        });

        // Calculate win rates
        Object.keys(hourlyStats).forEach(hour => {
            const stats = hourlyStats[hour];
            stats.winRate = (stats.wins / stats.trades) * 100;
            stats.avgProfit = stats.totalProfit / stats.trades;
        });

        return { hourlyStats };
    }

    /**
     * Analyze symbol performance
     * @returns {Object} Symbol analysis
     */
    analyzeSymbolPerformance() {
        const symbolStats = {};
        
        this.trades.forEach(trade => {
            const symbol = trade.symbol;
            
            if (!symbolStats[symbol]) {
                symbolStats[symbol] = {
                    trades: 0,
                    wins: 0,
                    totalProfit: 0,
                    avgHoldTime: 0,
                    lastTraded: 0
                };
            }
            
            const stats = symbolStats[symbol];
            stats.trades++;
            if (trade.netProfit > 0) stats.wins++;
            stats.totalProfit += trade.netProfit;
            stats.lastTraded = Math.max(stats.lastTraded, trade.exitTime);
        });

        // Calculate derived metrics
        Object.keys(symbolStats).forEach(symbol => {
            const stats = symbolStats[symbol];
            const symbolTrades = this.trades.filter(t => t.symbol === symbol);
            
            stats.winRate = (stats.wins / stats.trades) * 100;
            stats.avgProfit = stats.totalProfit / stats.trades;
            stats.avgHoldTime = symbolTrades.reduce((sum, t) => sum + t.holdingTime, 0) / stats.trades;
        });

        return symbolStats;
    }

    /**
     * Save trade to file
     * @param {Object} trade - Trade to save
     */
    saveTradeToFile(trade) {
        try {
            fs.writeFileSync(this.tradesFile, JSON.stringify(this.trades, null, 2));
        } catch (error) {
            console.error('❌ Error saving trade to file:', error.message);
        }
    }

    /**
     * Save daily statistics
     */
    saveDailyStats() {
        try {
            fs.writeFileSync(this.dailyStatsFile, JSON.stringify(this.dailyStats, null, 2));
        } catch (error) {
            console.error('❌ Error saving daily stats:', error.message);
        }
    }

    /**
     * Save performance metrics
     */
    savePerformanceMetrics() {
        try {
            fs.writeFileSync(this.performanceFile, JSON.stringify(this.performanceMetrics, null, 2));
        } catch (error) {
            console.error('❌ Error saving performance metrics:', error.message);
        }
    }

    /**
     * Log trade to console with formatting
     * @param {Object} trade - Trade to log
     */
    logTradeToConsole(trade) {
        const isWin = trade.netProfit > 0;
        const emoji = isWin ? '✅' : '❌';
        
        console.log(`\n${emoji} TRADE LOGGED:`);
        console.log(`   ID: #${trade.id}`);
        console.log(`   Symbol: ${trade.symbol}`);
        console.log(`   P&L: ${trade.netProfit > 0 ? '+' : ''}$${trade.netProfit.toFixed(4)}`);
        console.log(`   Exit Reason: ${trade.exitReason}`);
        console.log(`   Duration: ${Math.round(trade.holdingTime / 1000)}s`);
        
        if (trade.signal) {
            console.log(`   Entry Signal: ${trade.signal.signalType} (${trade.signal.confidence.toFixed(1)}%)`);
        }
    }

    /**
     * Get session ID for grouping trades
     * @returns {string} Session ID
     */
    getSessionId() {
        return `session_${new Date(this.sessionStart).toISOString().replace(/[:.]/g, '-')}`;
    }

    /**
     * Get trading statistics
     * @returns {Object} Current statistics
     */
    getStatistics() {
        return {
            totalTrades: this.trades.length,
            performanceMetrics: this.performanceMetrics,
            dailyStats: this.dailyStats,
            sessionStart: this.sessionStart
        };
    }

    /**
     * Get recent trades
     * @param {number} limit - Number of recent trades
     * @returns {Array} Recent trades
     */
    getRecentTrades(limit = 10) {
        return this.trades.slice(-limit).reverse();
    }

    /**
     * Export trades to CSV
     * @param {string} filename - Output filename
     */
    exportToCSV(filename = null) {
        if (!filename) {
            filename = `trades_${new Date().toISOString().split('T')[0]}.csv`;
        }

        const csvHeaders = [
            'ID', 'Symbol', 'Entry Price', 'Exit Price', 'Quantity',
            'Entry Time', 'Exit Time', 'Holding Time (s)', 'Net Profit',
            'Profit %', 'Exit Reason', 'Total Fees', 'Balance After'
        ];

        const csvRows = this.trades.map(trade => [
            trade.id,
            trade.symbol,
            trade.entryPrice,
            trade.exitPrice,
            trade.quantity,
            new Date(trade.entryTime).toISOString(),
            new Date(trade.exitTime).toISOString(),
            Math.round(trade.holdingTime / 1000),
            trade.netProfit,
            trade.profitPercent,
            trade.exitReason,
            trade.totalFees,
            trade.balanceAfter
        ]);

        const csvContent = [csvHeaders, ...csvRows]
            .map(row => row.join(','))
            .join('\n');

        const csvPath = path.join(this.logsDir, filename);
        fs.writeFileSync(csvPath, csvContent);
        
        console.log(`📊 Trades exported to: ${csvPath}`);
        return csvPath;
    }
}

module.exports = TradeLogger;
