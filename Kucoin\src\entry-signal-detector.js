/**
 * Entry Signal Detector
 * Detects trading entry signals based on our strategy:
 * 1. Coin in top 5 gainers for 3+ minutes
 * 2. Price drops 0.1-0.3% from recent high (pullback)
 * 3. Price breaks back above pullback + 0.03%
 * 4. Volume confirms the breakout
 */
class EntrySignalDetector {
    constructor() {
        // Strategy parameters
        this.TOP_GAINERS_REQUIREMENT = 5; // Must be in top 5
        this.MIN_GAINER_TIME = 180000; // 3 minutes in milliseconds
        this.MIN_PULLBACK = 0.001; // 0.1%
        this.MAX_PULLBACK = 0.003; // 0.3%
        this.BREAKOUT_THRESHOLD = 0.0003; // 0.03%
        this.MIN_VOLUME_RATIO = 1.2; // Breakout volume must be 20% higher than pullback
        
        // Data tracking
        this.gainerHistory = new Map(); // Track when coins entered top gainers
        this.priceHistory = new Map(); // Track price movements for each coin
        this.volumeHistory = new Map(); // Track volume data
        this.signalCooldowns = new Map(); // Prevent duplicate signals
        
        // Signal tracking
        this.lastSignalTime = 0;
        this.totalSignals = 0;
        this.validSignals = 0;
        
        console.log('🎯 Entry Signal Detector initialized');
        console.log(`📊 Strategy: Top ${this.TOP_GAINERS_REQUIREMENT} gainers, ${this.MIN_PULLBACK*100}%-${this.MAX_PULLBACK*100}% pullback, ${this.BREAKOUT_THRESHOLD*100}% breakout`);
    }

    /**
     * Update gainer tracking data
     * @param {Array} topGainers - Current top gainers list
     * @param {number} timestamp - Current timestamp
     */
    updateGainerTracking(topGainers, timestamp = Date.now()) {
        // Track when coins enter top gainers
        topGainers.forEach((gainer, index) => {
            const symbol = gainer.symbol;
            const rank = index + 1;
            
            if (rank <= this.TOP_GAINERS_REQUIREMENT) {
                if (!this.gainerHistory.has(symbol)) {
                    // New coin in top gainers
                    this.gainerHistory.set(symbol, {
                        firstSeenTime: timestamp,
                        rank: rank,
                        entryPrice: gainer.price,
                        qualified: false
                    });
                } else {
                    // Update existing coin
                    const history = this.gainerHistory.get(symbol);
                    history.rank = rank;
                    
                    // Check if coin has been in top gainers long enough
                    const timeInGainers = timestamp - history.firstSeenTime;
                    if (timeInGainers >= this.MIN_GAINER_TIME) {
                        history.qualified = true;
                    }
                }
            }
        });

        // Clean up coins that are no longer in top gainers
        this.cleanupOldGainers(topGainers, timestamp);
    }

    /**
     * Update price history for signal detection
     * @param {Array} tickers - Current ticker data
     * @param {number} timestamp - Current timestamp
     */
    updatePriceHistory(tickers, timestamp = Date.now()) {
        tickers.forEach(ticker => {
            const symbol = ticker.symbol;
            const price = parseFloat(ticker.last);
            const volume = parseFloat(ticker.volValue || 0);
            
            // Initialize price history if not exists
            if (!this.priceHistory.has(symbol)) {
                this.priceHistory.set(symbol, []);
            }
            
            if (!this.volumeHistory.has(symbol)) {
                this.volumeHistory.set(symbol, []);
            }
            
            // Add current price data
            const priceData = {
                price: price,
                timestamp: timestamp,
                volume: volume
            };
            
            const priceHistory = this.priceHistory.get(symbol);
            const volumeHistory = this.volumeHistory.get(symbol);
            
            priceHistory.push(priceData);
            volumeHistory.push({ volume: volume, timestamp: timestamp });
            
            // Keep only last 10 minutes of data (600 seconds)
            const cutoffTime = timestamp - 600000;
            this.priceHistory.set(symbol, priceHistory.filter(p => p.timestamp > cutoffTime));
            this.volumeHistory.set(symbol, volumeHistory.filter(v => v.timestamp > cutoffTime));
        });
    }

    /**
     * Detect entry signals for qualified coins
     * @param {number} timestamp - Current timestamp
     * @returns {Array} Array of detected signals
     */
    detectEntrySignals(timestamp = Date.now()) {
        const signals = [];
        
        // Check each qualified gainer for entry signals
        for (const [symbol, gainerData] of this.gainerHistory.entries()) {
            if (!gainerData.qualified) continue;
            
            // Check cooldown (prevent duplicate signals within 5 minutes)
            const cooldownKey = symbol;
            if (this.signalCooldowns.has(cooldownKey)) {
                const lastSignal = this.signalCooldowns.get(cooldownKey);
                if (timestamp - lastSignal < 300000) { // 5 minutes
                    continue;
                }
            }
            
            const signal = this.analyzeSymbolForEntry(symbol, timestamp);
            if (signal) {
                signals.push(signal);
                this.signalCooldowns.set(cooldownKey, timestamp);
                this.totalSignals++;
                
                console.log(`🎯 ENTRY SIGNAL DETECTED: ${symbol}`);
                console.log(`   Rank: #${gainerData.rank}`);
                console.log(`   Entry Price: $${signal.entryPrice.toFixed(6)}`);
                console.log(`   Signal Type: ${signal.signalType}`);
                console.log(`   Confidence: ${signal.confidence.toFixed(1)}%`);
            }
        }
        
        return signals;
    }

    /**
     * Analyze a specific symbol for entry conditions
     * @param {string} symbol - Symbol to analyze
     * @param {number} timestamp - Current timestamp
     * @returns {Object|null} Signal object or null
     */
    analyzeSymbolForEntry(symbol, timestamp) {
        const priceHistory = this.priceHistory.get(symbol);
        const volumeHistory = this.volumeHistory.get(symbol);
        
        if (!priceHistory || priceHistory.length < 10) {
            return null; // Need sufficient price history
        }
        
        // Get recent price data (last 2 minutes)
        const recentCutoff = timestamp - 120000; // 2 minutes
        const recentPrices = priceHistory.filter(p => p.timestamp > recentCutoff);
        
        if (recentPrices.length < 5) {
            return null; // Need at least 5 recent price points
        }
        
        // Find recent high and current price
        const currentPrice = recentPrices[recentPrices.length - 1].price;
        const recentHigh = Math.max(...recentPrices.map(p => p.price));
        const recentHighTime = recentPrices.find(p => p.price === recentHigh).timestamp;
        
        // Check for pullback pattern
        const pullbackPercent = (recentHigh - currentPrice) / recentHigh;
        
        // Must have had a pullback in the valid range
        if (pullbackPercent < this.MIN_PULLBACK || pullbackPercent > this.MAX_PULLBACK) {
            return null;
        }
        
        // Find the pullback low
        const pullbackPrices = recentPrices.filter(p => p.timestamp > recentHighTime);
        if (pullbackPrices.length < 3) {
            return null; // Need some price action after the high
        }
        
        const pullbackLow = Math.min(...pullbackPrices.map(p => p.price));
        const breakoutLevel = pullbackLow * (1 + this.BREAKOUT_THRESHOLD);
        
        // Check if current price is breaking out above pullback + threshold
        if (currentPrice < breakoutLevel) {
            return null; // No breakout yet
        }
        
        // Check volume confirmation
        const volumeConfirmed = this.checkVolumeConfirmation(symbol, recentHighTime, timestamp);
        if (!volumeConfirmed) {
            return null; // Volume doesn't confirm breakout
        }
        
        // Check for consecutive green ticks (momentum confirmation)
        const lastThreePrices = recentPrices.slice(-3).map(p => p.price);
        const hasGreenMomentum = lastThreePrices.length >= 2 && 
                                lastThreePrices[lastThreePrices.length - 1] > lastThreePrices[lastThreePrices.length - 2];
        
        if (!hasGreenMomentum) {
            return null; // No upward momentum
        }
        
        // Calculate signal confidence
        const confidence = this.calculateSignalConfidence(symbol, pullbackPercent, volumeConfirmed);
        
        // Create signal object
        const signal = {
            symbol: symbol,
            entryPrice: currentPrice,
            timestamp: timestamp,
            signalType: 'PULLBACK_BREAKOUT',
            recentHigh: recentHigh,
            pullbackLow: pullbackLow,
            pullbackPercent: pullbackPercent * 100,
            breakoutLevel: breakoutLevel,
            confidence: confidence,
            gainerRank: this.gainerHistory.get(symbol).rank,
            volumeConfirmed: volumeConfirmed
        };
        
        this.validSignals++;
        return signal;
    }

    /**
     * Check volume confirmation for breakout
     * @param {string} symbol - Symbol to check
     * @param {number} recentHighTime - Time of recent high
     * @param {number} currentTime - Current timestamp
     * @returns {boolean} True if volume confirms breakout
     */
    checkVolumeConfirmation(symbol, recentHighTime, currentTime) {
        const volumeHistory = this.volumeHistory.get(symbol);
        if (!volumeHistory || volumeHistory.length < 5) {
            return false;
        }
        
        // Get volume during pullback and breakout
        const pullbackVolumes = volumeHistory.filter(v => 
            v.timestamp > recentHighTime && v.timestamp < currentTime - 30000
        );
        
        const breakoutVolumes = volumeHistory.filter(v => 
            v.timestamp > currentTime - 30000
        );
        
        if (pullbackVolumes.length === 0 || breakoutVolumes.length === 0) {
            return false;
        }
        
        const avgPullbackVolume = pullbackVolumes.reduce((sum, v) => sum + v.volume, 0) / pullbackVolumes.length;
        const avgBreakoutVolume = breakoutVolumes.reduce((sum, v) => sum + v.volume, 0) / breakoutVolumes.length;
        
        // Breakout volume should be higher than pullback volume
        return avgBreakoutVolume > avgPullbackVolume * this.MIN_VOLUME_RATIO;
    }

    /**
     * Calculate signal confidence score
     * @param {string} symbol - Symbol
     * @param {number} pullbackPercent - Pullback percentage
     * @param {boolean} volumeConfirmed - Volume confirmation
     * @returns {number} Confidence score (0-100)
     */
    calculateSignalConfidence(symbol, pullbackPercent, volumeConfirmed) {
        let confidence = 50; // Base confidence
        
        // Gainer rank bonus
        const gainerData = this.gainerHistory.get(symbol);
        if (gainerData.rank <= 3) confidence += 20;
        else if (gainerData.rank <= 5) confidence += 10;
        
        // Pullback size bonus (ideal range)
        if (pullbackPercent >= 0.0015 && pullbackPercent <= 0.0025) {
            confidence += 15; // 0.15-0.25% is ideal pullback
        }
        
        // Volume confirmation bonus
        if (volumeConfirmed) confidence += 15;
        
        // Time in gainers bonus
        const timeInGainers = Date.now() - gainerData.firstSeenTime;
        if (timeInGainers > 300000) confidence += 10; // 5+ minutes
        
        return Math.min(confidence, 100);
    }

    /**
     * Clean up old gainer data
     * @param {Array} currentTopGainers - Current top gainers
     * @param {number} timestamp - Current timestamp
     */
    cleanupOldGainers(currentTopGainers, timestamp) {
        const currentSymbols = new Set(currentTopGainers.map(g => g.symbol));
        const cutoffTime = timestamp - 1800000; // 30 minutes
        
        // Remove coins no longer in top gainers or too old
        for (const [symbol, data] of this.gainerHistory.entries()) {
            if (!currentSymbols.has(symbol) || data.firstSeenTime < cutoffTime) {
                this.gainerHistory.delete(symbol);
            }
        }
        
        // Clean up old cooldowns
        for (const [symbol, lastSignal] of this.signalCooldowns.entries()) {
            if (timestamp - lastSignal > 600000) { // 10 minutes
                this.signalCooldowns.delete(symbol);
            }
        }
    }

    /**
     * Get detector statistics
     * @returns {Object} Statistics object
     */
    getStatistics() {
        return {
            totalSignals: this.totalSignals,
            validSignals: this.validSignals,
            qualifiedGainers: Array.from(this.gainerHistory.values()).filter(g => g.qualified).length,
            trackedSymbols: this.gainerHistory.size,
            signalRate: this.totalSignals > 0 ? (this.validSignals / this.totalSignals) * 100 : 0
        };
    }

    /**
     * Get currently qualified gainers
     * @returns {Array} Array of qualified gainer symbols
     */
    getQualifiedGainers() {
        const qualified = [];
        for (const [symbol, data] of this.gainerHistory.entries()) {
            if (data.qualified) {
                qualified.push({
                    symbol: symbol,
                    rank: data.rank,
                    timeInGainers: Date.now() - data.firstSeenTime,
                    entryPrice: data.entryPrice
                });
            }
        }
        return qualified.sort((a, b) => a.rank - b.rank);
    }
}

module.exports = EntrySignalDetector;
