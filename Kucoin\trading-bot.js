#!/usr/bin/env node

/**
 * KuCoin Trading Bot V2 - Paper Trading
 * Automated trading bot with entry signal detection and position management
 * 
 * Features:
 * - Paper trading with 100 USDT starting balance
 * - Entry conditions: Top 5 gainers, pullback patterns, volume confirmation
 * - Exit conditions: 0.56% profit target, 0.15% stop loss, 3 red ticks
 * - Comprehensive trade logging and analytics
 * - Real-time web dashboard
 */

const KuCoinAPI = require('./src/kucoin-api');
const DataProcessor = require('./src/data-processor');
const ErrorHandler = require('./src/error-handler');
const WebServer = require('./src/web-server');
const PaperTradingEngine = require('./src/paper-trading-engine');
const EntrySignalDetector = require('./src/entry-signal-detector');
const PositionManager = require('./src/position-manager');
const TradeLogger = require('./src/trade-logger');

class TradingBot {
    constructor() {
        // Core components
        this.api = new KuCoinAPI();
        this.dataProcessor = new DataProcessor();
        this.errorHandler = new ErrorHandler();
        this.webServer = new WebServer(3001); // Use different port for trading dashboard
        
        // Trading components
        this.tradingEngine = new PaperTradingEngine();
        this.signalDetector = new EntrySignalDetector();
        this.positionManager = new PositionManager(this.tradingEngine, this.signalDetector);
        this.tradeLogger = new TradeLogger();
        
        // Data and state
        this.currentTopGainers = [];
        this.currentTickers = [];
        this.isRunning = false;
        this.lastUpdate = null;
        
        // Update intervals
        this.DATA_UPDATE_INTERVAL = 2000; // 2 seconds for data updates
        this.TRADING_UPDATE_INTERVAL = 1000; // 1 second for trading logic
        this.DASHBOARD_UPDATE_INTERVAL = 1500; // 1.5 seconds for dashboard
        
        // Timers
        this.dataUpdateTimer = null;
        this.tradingUpdateTimer = null;
        this.dashboardUpdateTimer = null;
        
        console.log('🤖 KuCoin Trading Bot V2 initialized');
        console.log('💰 Paper Trading Mode - Starting Balance: $100 USDT');
    }

    /**
     * Start the trading bot
     */
    async start() {
        try {
            console.log('🚀 Starting KuCoin Trading Bot V2...');
            
            // Test API connection
            const isConnected = await this.api.testConnection();
            if (!isConnected) {
                throw new Error('Failed to connect to KuCoin API');
            }
            console.log('✅ KuCoin API connection established');
            
            // Start web server
            await this.webServer.start();
            console.log('🌐 Trading dashboard available at: http://localhost:3001');
            
            // Initial data fetch
            await this.updateMarketData();
            console.log('📊 Initial market data loaded');
            
            // Start position monitoring
            this.positionManager.startMonitoring(this.TRADING_UPDATE_INTERVAL);
            console.log('👁️ Position monitoring started');
            
            // Start update timers
            this.startUpdateTimers();
            
            this.isRunning = true;
            console.log('✅ Trading Bot V2 started successfully!');
            console.log('📈 Ready to detect signals and execute trades');
            
            // Log initial status
            this.logStatus();
            
        } catch (error) {
            const shouldContinue = this.errorHandler.handleError(error, 'Bot Startup', true);
            if (!shouldContinue) {
                console.error('❌ Failed to start trading bot - terminating');
                process.exit(1);
            }
        }
    }

    /**
     * Start all update timers
     */
    startUpdateTimers() {
        // Data update timer (fetch market data)
        this.dataUpdateTimer = setInterval(() => {
            if (this.isRunning) {
                this.updateMarketData();
            }
        }, this.DATA_UPDATE_INTERVAL);

        // Dashboard update timer (update web dashboard)
        this.dashboardUpdateTimer = setInterval(() => {
            if (this.isRunning) {
                this.updateDashboard();
            }
        }, this.DASHBOARD_UPDATE_INTERVAL);

        console.log('⏰ Update timers started');
    }

    /**
     * Update market data from KuCoin API
     */
    async updateMarketData() {
        const wrappedUpdate = this.errorHandler.wrapAsync(async () => {
            // Fetch all tickers
            const fetchTickers = this.errorHandler.withRetry(
                () => this.api.getAllTickers(),
                3,
                2000
            );
            
            const tickers = await fetchTickers();
            if (!tickers) {
                throw new Error('Failed to fetch tickers after retries');
            }
            
            // Process data to get top gainers using current strategy
            this.currentTopGainers = this.dataProcessor.getTopGainersWithStrategy(tickers, 10);
            this.currentTickers = tickers;
            this.lastUpdate = Date.now();
            
            // Update trading components with new data
            this.positionManager.updateMarketData(
                this.currentTopGainers, 
                this.currentTickers, 
                this.lastUpdate
            );
            
            this.errorHandler.reset(); // Reset error count on successful update
            
        }, 'Update Market Data', false);
        
        await wrappedUpdate();
    }

    /**
     * Update web dashboard with current data
     */
    updateDashboard() {
        try {
            // Get trading statistics
            const tradingStats = this.positionManager.getStatistics();
            const loggerStats = this.tradeLogger.getStatistics();
            
            // Prepare dashboard data
            const dashboardData = {
                // Market data
                topGainers: this.currentTopGainers,
                lastUpdate: this.lastUpdate,
                
                // Trading data
                currentPosition: this.positionManager.getCurrentPosition(),
                qualifiedGainers: this.positionManager.getQualifiedGainers(),
                
                // Statistics
                balance: tradingStats.currentBalance,
                totalTrades: tradingStats.totalTrades,
                winRate: tradingStats.winRate,
                dailyProfit: tradingStats.dailyProfit,
                isInTrade: tradingStats.isInTrade,
                
                // Recent trades
                recentTrades: this.tradeLogger.getRecentTrades(5),
                
                // Performance metrics
                performanceMetrics: loggerStats.performanceMetrics
            };
            
            // Broadcast to web dashboard
            this.webServer.io.emit('trading-update', dashboardData);
            
        } catch (error) {
            console.error('❌ Error updating dashboard:', error.message);
        }
    }

    /**
     * Log current bot status
     */
    logStatus() {
        const stats = this.positionManager.getStatistics();
        
        console.log('\n📊 TRADING BOT STATUS:');
        console.log(`   💰 Balance: $${stats.currentBalance.toFixed(2)}`);
        console.log(`   📈 Total Trades: ${stats.totalTrades}`);
        console.log(`   🎯 Win Rate: ${stats.winRate.toFixed(1)}%`);
        console.log(`   💵 Daily P&L: ${stats.dailyProfit > 0 ? '+' : ''}$${stats.dailyProfit.toFixed(2)}`);
        console.log(`   🔄 In Trade: ${stats.isInTrade ? 'YES' : 'NO'}`);
        console.log(`   🎯 Qualified Gainers: ${stats.qualifiedGainers}`);
        console.log(`   📡 Signals Detected: ${stats.totalSignals}`);
        
        if (stats.isInTrade) {
            const position = this.positionManager.getCurrentPosition();
            console.log(`   📍 Current Position: ${position.symbol} @ $${position.entryPrice.toFixed(6)}`);
        }
    }

    /**
     * Handle trade completion (called by position manager)
     * @param {Object} trade - Completed trade
     * @param {Object} signal - Original entry signal
     */
    onTradeCompleted(trade, signal) {
        // Log trade with comprehensive data
        this.tradeLogger.logTrade(trade, signal, {
            topGainersAtEntry: this.currentTopGainers.slice(0, 5),
            marketCondition: 'normal', // Could be enhanced with market analysis
            timestamp: trade.exitTime
        });
        
        // Update dashboard immediately
        this.updateDashboard();
        
        // Log status update
        setTimeout(() => this.logStatus(), 1000);
    }

    /**
     * Stop the trading bot
     */
    async stop() {
        console.log('🛑 Stopping Trading Bot V2...');
        
        this.isRunning = false;
        
        // Stop position monitoring
        this.positionManager.stopMonitoring();
        
        // Clear timers
        if (this.dataUpdateTimer) {
            clearInterval(this.dataUpdateTimer);
            this.dataUpdateTimer = null;
        }
        
        if (this.dashboardUpdateTimer) {
            clearInterval(this.dashboardUpdateTimer);
            this.dashboardUpdateTimer = null;
        }
        
        // Force exit any open position
        if (this.tradingEngine.isInTrade) {
            console.log('🏁 Force closing open position...');
            await this.positionManager.forceExit('Bot shutdown');
        }
        
        // Stop web server
        await this.webServer.stop();
        
        // Final statistics
        const finalStats = this.positionManager.getStatistics();
        console.log('\n📊 FINAL TRADING STATISTICS:');
        console.log(`   💰 Final Balance: $${finalStats.currentBalance.toFixed(2)}`);
        console.log(`   📈 Total Trades: ${finalStats.totalTrades}`);
        console.log(`   🎯 Win Rate: ${finalStats.winRate.toFixed(1)}%`);
        console.log(`   💵 Total P&L: ${finalStats.netProfit > 0 ? '+' : ''}$${finalStats.netProfit.toFixed(2)}`);
        console.log(`   📊 Return: ${((finalStats.currentBalance - 100) / 100 * 100).toFixed(2)}%`);
        
        // Export trades to CSV
        this.tradeLogger.exportToCSV();
        
        console.log('✅ Trading Bot V2 stopped gracefully');
    }

    /**
     * Get current bot statistics
     * @returns {Object} Bot statistics
     */
    getStatistics() {
        return {
            ...this.positionManager.getStatistics(),
            ...this.tradeLogger.getStatistics(),
            isRunning: this.isRunning,
            lastUpdate: this.lastUpdate,
            topGainersCount: this.currentTopGainers.length
        };
    }

    /**
     * Manual trade execution (for testing)
     * @param {string} symbol - Symbol to trade
     * @param {number} price - Entry price
     */
    async manualTrade(symbol, price) {
        if (!this.tradingEngine.canTrade()) {
            console.log('❌ Cannot execute manual trade: Already in position or insufficient balance');
            return;
        }
        
        console.log(`🔧 Manual trade execution: ${symbol} @ $${price}`);
        
        const result = this.tradingEngine.enterPosition(symbol, price);
        if (result.success) {
            console.log('✅ Manual trade executed successfully');
        } else {
            console.log(`❌ Manual trade failed: ${result.message}`);
        }
    }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Received SIGINT, shutting down gracefully...');
    if (global.tradingBot) {
        await global.tradingBot.stop();
    }
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
    if (global.tradingBot) {
        await global.tradingBot.stop();
    }
    process.exit(0);
});

// Start the trading bot
async function main() {
    try {
        const bot = new TradingBot();
        global.tradingBot = bot;
        await bot.start();
        
        // Log status every 5 minutes
        setInterval(() => {
            if (bot.isRunning) {
                bot.logStatus();
            }
        }, 300000); // 5 minutes
        
    } catch (error) {
        console.error('💥 Fatal error:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = TradingBot;
