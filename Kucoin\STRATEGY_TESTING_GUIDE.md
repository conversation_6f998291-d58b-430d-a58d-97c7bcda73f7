# Strategy Testing Guide

## 🎯 Problem Solved
- **Fixed**: Dashboard showed trades but they weren't visible in the table
- **Added**: Strategy toggle to test each strategy separately  
- **Added**: Reset function to start fresh with clean balance and logs

## 🎮 How to Control the Bot

### Method 1: Command Line Controller
```bash
node bot-control.js
```

**Available Commands:**
- `velocity` - Switch to VELOCITY strategy only (3-sec ≥0.30%)
- `thrust` - Switch to THRUST strategy only (5-sec ≥0.20%) 
- `both` - Use BOTH strategies (default)
- `reset` - Reset bot (balance, logs, stats)
- `status` - Show current status
- `exit` - Exit controller

### Method 2: Web Dashboard
- Open: http://localhost:3002/trading
- Use the strategy control buttons in the dashboard
- Click "Reset Bot" to start fresh

## 📊 Testing Strategy Performance

### Recommended Testing Process:
1. **Reset the bot** to start with fresh $100 balance
2. **Switch to VELOCITY only** and run for 15 minutes
3. **Note the performance** (trades, win rate, P&L)
4. **Reset again** and switch to **THRUST only** for 15 minutes  
5. **Compare results** to see which strategy performs better

### Strategy Details:

**VELOCITY Strategy:**
- Detects 3-second price moves ≥0.30%
- Higher threshold, fewer but stronger signals
- Good for catching momentum breakouts

**THRUST Strategy:**  
- Detects 5-second price moves ≥0.20%
- Lower threshold, more frequent signals
- Good for catching smaller momentum moves

**BOTH Strategy:**
- Uses velocity first, thrust as backup
- Maximum signal coverage
- Default balanced approach

## 🔄 Quick Commands

**Start Bot:**
```bash
node velocity-trading-bot.js
```

**Control Bot:**
```bash
node bot-control.js
```

**Or use the batch file:**
```bash
control.bat
```

## 📈 Performance Monitoring

**Dashboard URL:** http://localhost:3002/trading

**Key Metrics to Watch:**
- Win Rate (target: >60%)
- Daily P&L (target: positive)
- Trade Frequency (target: 10-15+ trades/day)
- Average trade duration
- Profit factor

## 🎯 Testing Goals

**Target Performance:**
- 10-15+ successful trades per day
- Win rate >60%
- Minimal losses (losses erode gains quickly)
- Quick trade execution (seconds to minutes)

**Strategy Comparison:**
- Test each strategy for 15 minutes
- Record: trades executed, win rate, total P&L
- Choose the best performing strategy for longer runs

## 🚀 Current Status

✅ Bot is running and actively trading
✅ Strategy toggle working perfectly  
✅ Reset function working
✅ Dashboard showing real-time updates
✅ Both VELOCITY and THRUST strategies operational

**Ready for 15-minute strategy testing sessions!**
