/**
 * Test balance calculation fix
 */

const PaperTradingEngine = require('./src/paper-trading-engine');

console.log('🧪 TESTING BALANCE CALCULATION FIX');
console.log('==================================');

const engine = new PaperTradingEngine();

console.log(`Starting balance: $${engine.currentBalance.toFixed(2)}`);

// Enter a position
const entryPrice = 1.0000;
const entryResult = engine.enterPosition('TEST-USDT', entryPrice, Date.now());

if (entryResult.success) {
    console.log(`✅ Position entered at $${entryPrice.toFixed(6)}`);
    console.log(`   Quantity: ${entryResult.position.quantity.toFixed(6)}`);
    console.log(`   Position Value: $${entryResult.position.positionValue.toFixed(2)}`);
    console.log(`   Balance after entry: $${engine.currentBalance.toFixed(2)}`);
    
    // Test first half exit at 1.20% profit
    const firstExitPrice = entryPrice * 1.012; // 1.20% profit
    console.log(`\n📊 Testing first half exit at $${firstExitPrice.toFixed(6)}...`);
    
    const updateResult1 = engine.updatePosition(firstExitPrice, Date.now());
    if (updateResult1.exitCondition && updateResult1.exitCondition.type === 'FIRST_HALF_PROFIT') {
        console.log('✅ First half exit condition triggered');
        
        const exitResult1 = engine.exitPosition(
            firstExitPrice,
            updateResult1.exitCondition.reason,
            Date.now(),
            updateResult1.exitCondition
        );
        
        if (exitResult1.success) {
            console.log(`✅ First 50% exited successfully`);
            console.log(`   Exit Price: $${firstExitPrice.toFixed(6)}`);
            console.log(`   Profit: $${exitResult1.trade.netProfit.toFixed(4)}`);
            console.log(`   Balance after first exit: $${engine.currentBalance.toFixed(2)}`);
            console.log(`   Expected balance: ~$100.60 (should be close to starting balance + profit)`);
            
            // Check if balance is reasonable
            const expectedBalance = 100 + exitResult1.trade.netProfit;
            const balanceDiff = Math.abs(engine.currentBalance - expectedBalance);
            
            if (balanceDiff < 0.01) {
                console.log(`✅ Balance calculation FIXED! Difference: $${balanceDiff.toFixed(4)}`);
            } else {
                console.log(`❌ Balance calculation still broken! Difference: $${balanceDiff.toFixed(4)}`);
                console.log(`   Current: $${engine.currentBalance.toFixed(2)}`);
                console.log(`   Expected: $${expectedBalance.toFixed(2)}`);
            }
            
            // Test if position is still active for second half
            if (engine.isInTrade && engine.currentPosition) {
                console.log(`✅ Position still active for second half`);
                console.log(`   Remaining quantity: ${engine.currentPosition.remainingQuantity.toFixed(6)}`);
                console.log(`   First half exited: ${engine.currentPosition.firstHalfExited}`);
            } else {
                console.log(`❌ Position not active for second half - hybrid exit broken`);
            }
        }
    }
} else {
    console.log(`❌ Failed to enter position: ${entryResult.message}`);
}

console.log('\n📋 TEST SUMMARY');
console.log('===============');
console.log('This test checks if the balance calculation fix is working correctly.');
console.log('The balance should only increase by the profit amount, not the full exit value.');
