/**
 * Paper Trading Engine
 * Handles all paper trading operations including balance tracking, 
 * position management, and trade execution simulation
 */
class PaperTradingEngine {
    constructor() {
        // Initial trading parameters
        this.initialBalance = 100; // 100 USDT starting balance
        this.currentBalance = 100;
        this.feeRate = 0.0008; // 0.08% each way (0.16% total round trip)
        this.profitTarget = 0.012; // 1.20% profit target (proven achievable)
        this.stopLoss = 0.006; // 0.60% stop loss (wider protection)
        
        // Position tracking
        this.currentPosition = null;
        this.isInTrade = false;
        
        // Trade history and statistics
        this.tradeHistory = [];
        this.totalTrades = 0;
        this.winningTrades = 0;
        this.losingTrades = 0;
        this.totalProfit = 0;
        this.totalLoss = 0;
        this.maxDrawdown = 0;
        this.peakBalance = 100;
        
        // Performance tracking
        this.dailyTrades = 0;
        this.dailyProfit = 0;
        this.lastTradeTime = null;
        this.tradingStartTime = Date.now();
        
        console.log('📊 Paper Trading Engine initialized');
        console.log(`💰 Starting balance: ${this.currentBalance} USDT`);
        console.log(`🎯 Profit target: ${(this.profitTarget * 100).toFixed(2)}%`);
        console.log(`🛑 Stop loss: ${(this.stopLoss * 100).toFixed(2)}%`);
        console.log(`💸 Trading fees: ${(this.feeRate * 100).toFixed(2)}% each way`);
    }

    /**
     * Check if we can enter a new trade
     * @returns {boolean} True if we can trade
     */
    canTrade() {
        return !this.isInTrade && this.currentBalance > 1; // Need at least 1 USDT to trade
    }

    /**
     * Enter a new position
     * @param {string} symbol - Trading pair symbol
     * @param {number} entryPrice - Entry price
     * @param {number} timestamp - Entry timestamp
     * @returns {Object} Trade result
     */
    enterPosition(symbol, entryPrice, timestamp = Date.now()) {
        if (!this.canTrade()) {
            return {
                success: false,
                message: 'Cannot enter trade: Already in position or insufficient balance'
            };
        }

        // Calculate position size (use 100% of available balance)
        const availableBalance = this.currentBalance;
        const entryFee = availableBalance * this.feeRate;
        const positionValue = availableBalance - entryFee;
        const quantity = positionValue / entryPrice;

        // Create position object (simple exit strategy)
        this.currentPosition = {
            symbol: symbol,
            entryPrice: entryPrice,
            quantity: quantity,
            positionValue: positionValue,
            entryFee: entryFee,
            entryTime: timestamp,
            profitTarget: entryPrice * (1 + this.profitTarget),
            stopLossPrice: entryPrice * (1 - this.stopLoss),
            consecutiveRedTicks: 0,
            lastPrice: entryPrice
        };

        this.isInTrade = true;
        this.lastTradeTime = timestamp;

        console.log(`🚀 ENTERED POSITION:`);
        console.log(`   Symbol: ${symbol}`);
        console.log(`   Entry Price: $${entryPrice.toFixed(6)}`);
        console.log(`   Quantity: ${quantity.toFixed(6)}`);
        console.log(`   Position Value: $${positionValue.toFixed(2)}`);
        console.log(`   Entry Fee: $${entryFee.toFixed(4)}`);
        console.log(`   Profit Target: $${this.currentPosition.profitTarget.toFixed(6)}`);
        console.log(`   Stop Loss: $${this.currentPosition.stopLossPrice.toFixed(6)}`);

        return {
            success: true,
            position: this.currentPosition,
            message: `Entered ${symbol} at $${entryPrice.toFixed(6)}`
        };
    }

    /**
     * Update current position with new price data
     * @param {number} currentPrice - Current market price
     * @param {number} timestamp - Current timestamp
     * @returns {Object} Position update result
     */
    updatePosition(currentPrice, timestamp = Date.now()) {
        if (!this.isInTrade || !this.currentPosition) {
            return { success: false, message: 'No active position to update' };
        }

        const position = this.currentPosition;
        const previousPrice = position.lastPrice;
        
        // Update price tracking
        position.lastPrice = currentPrice;

        // Track consecutive red ticks
        if (currentPrice < previousPrice) {
            position.consecutiveRedTicks++;
        } else {
            position.consecutiveRedTicks = 0;
        }

        // Calculate current P&L
        const currentValue = position.quantity * currentPrice;
        const unrealizedPnL = currentValue - position.positionValue;
        const unrealizedPnLPercent = (unrealizedPnL / position.positionValue) * 100;

        // Check exit conditions (simple exit logic)
        const exitCondition = this.checkExitConditions(currentPrice);
        
        return {
            success: true,
            currentPrice: currentPrice,
            unrealizedPnL: unrealizedPnL,
            unrealizedPnLPercent: unrealizedPnLPercent,
            exitCondition: exitCondition,
            consecutiveRedTicks: position.consecutiveRedTicks
        };
    }

    /**
     * Check if any exit conditions are met (simple 1.20% exit)
     * @param {number} currentPrice - Current market price
     * @returns {Object|null} Exit condition details or null
     */
    checkExitConditions(currentPrice) {
        if (!this.currentPosition) return null;

        const position = this.currentPosition;

        // Check profit target
        if (currentPrice >= position.profitTarget) {
            return {
                type: 'PROFIT_TARGET',
                reason: 'Profit target reached',
                price: currentPrice
            };
        }

        // Check stop loss with slippage protection
        if (currentPrice <= position.stopLossPrice) {
            // CRITICAL FIX: Protect against excessive slippage
            const maxSlippage = 0.001; // 0.1% maximum slippage
            const worstExitPrice = position.stopLossPrice * (1 - maxSlippage);
            const protectedPrice = Math.max(currentPrice, worstExitPrice);

            return {
                type: 'STOP_LOSS',
                reason: 'Stop loss triggered',
                price: protectedPrice
            };
        }

        // Check consecutive red ticks (3 in a row)
        if (position.consecutiveRedTicks >= 3) {
            return {
                type: 'RED_TICKS',
                reason: '3 consecutive red ticks',
                price: currentPrice
            };
        }

        return null;
    }

    /**
     * Exit current position (simple 1.20% exit)
     * @param {number} exitPrice - Exit price
     * @param {string} exitReason - Reason for exit
     * @param {number} timestamp - Exit timestamp
     * @returns {Object} Trade result
     */
    exitPosition(exitPrice, exitReason, timestamp = Date.now()) {
        if (!this.isInTrade || !this.currentPosition) {
            return {
                success: false,
                message: 'No active position to exit'
            };
        }

        const position = this.currentPosition;

        // Calculate trade results (simple full exit)
        const exitValue = position.quantity * exitPrice;
        const exitFee = exitValue * this.feeRate;
        const netExitValue = exitValue - exitFee;
        const grossProfit = exitValue - position.positionValue;
        const netProfit = netExitValue - position.positionValue;
        const profitPercent = (netProfit / position.positionValue) * 100;
        const holdingTime = timestamp - position.entryTime;

        // Update balance with only the profit/loss (not the full exit value)
        this.currentBalance += netProfit;

        // Create trade record
        const trade = {
            id: this.totalTrades + 1,
            symbol: position.symbol,
            entryPrice: position.entryPrice,
            exitPrice: exitPrice,
            quantity: position.quantity,
            entryTime: position.entryTime,
            exitTime: timestamp,
            holdingTime: holdingTime,
            entryFee: position.entryFee,
            exitFee: exitFee,
            totalFees: position.entryFee + exitFee,
            grossProfit: grossProfit,
            netProfit: netProfit,
            profitPercent: profitPercent,
            exitReason: exitReason,
            balanceAfter: this.currentBalance
        };

        // Update statistics
        this.updateStatistics(trade);

        // Add to trade history
        this.tradeHistory.push(trade);

        // Reset position
        this.currentPosition = null;
        this.isInTrade = false;

        // Log trade result
        this.logTradeResult(trade);

        return {
            success: true,
            trade: trade,
            newBalance: this.currentBalance,
            message: `Exited ${position.symbol} at $${exitPrice.toFixed(6)} - ${exitReason}`
        };
    }

    /**
     * Update trading statistics
     * @param {Object} trade - Completed trade object
     */
    updateStatistics(trade) {
        this.totalTrades++;
        this.dailyTrades++;

        if (trade.netProfit > 0) {
            this.winningTrades++;
            this.totalProfit += trade.netProfit;
            this.dailyProfit += trade.netProfit;
        } else {
            this.losingTrades++;
            this.totalLoss += Math.abs(trade.netProfit);
            this.dailyProfit += trade.netProfit; // Add negative value
        }

        // Update peak balance and drawdown
        if (this.currentBalance > this.peakBalance) {
            this.peakBalance = this.currentBalance;
        }

        const currentDrawdown = ((this.peakBalance - this.currentBalance) / this.peakBalance) * 100;
        if (currentDrawdown > this.maxDrawdown) {
            this.maxDrawdown = currentDrawdown;
        }
    }

    /**
     * Log trade result to console
     * @param {Object} trade - Completed trade object
     */
    logTradeResult(trade) {
        const isWin = trade.netProfit > 0;
        const emoji = isWin ? '✅' : '❌';
        const color = isWin ? '\x1b[32m' : '\x1b[31m'; // Green or Red
        const reset = '\x1b[0m';

        console.log(`\n${emoji} TRADE COMPLETED:`);
        console.log(`   ${color}Symbol: ${trade.symbol}${reset}`);
        console.log(`   Entry: $${trade.entryPrice.toFixed(6)} → Exit: $${trade.exitPrice.toFixed(6)}`);
        console.log(`   ${color}P&L: ${trade.netProfit > 0 ? '+' : ''}$${trade.netProfit.toFixed(4)} (${trade.profitPercent.toFixed(2)}%)${reset}`);
        console.log(`   Reason: ${trade.exitReason}`);
        console.log(`   Fees: $${trade.totalFees.toFixed(4)}`);
        console.log(`   Duration: ${Math.round(trade.holdingTime / 1000)}s`);
        console.log(`   💰 New Balance: $${this.currentBalance.toFixed(2)}`);
        console.log(`   📊 Win Rate: ${this.getWinRate().toFixed(1)}% (${this.winningTrades}/${this.totalTrades})`);
    }

    /**
     * Get current trading statistics
     * @returns {Object} Trading statistics
     */
    getStatistics() {
        const winRate = this.getWinRate();
        const avgWin = this.winningTrades > 0 ? this.totalProfit / this.winningTrades : 0;
        const avgLoss = this.losingTrades > 0 ? this.totalLoss / this.losingTrades : 0;
        const profitFactor = this.totalLoss > 0 ? this.totalProfit / this.totalLoss : 0;
        const totalReturn = ((this.currentBalance - this.initialBalance) / this.initialBalance) * 100;

        return {
            currentBalance: this.currentBalance,
            initialBalance: this.initialBalance,
            totalReturn: totalReturn,
            totalTrades: this.totalTrades,
            winningTrades: this.winningTrades,
            losingTrades: this.losingTrades,
            winRate: winRate,
            totalProfit: this.totalProfit,
            totalLoss: this.totalLoss,
            netProfit: this.totalProfit - this.totalLoss,
            avgWin: avgWin,
            avgLoss: avgLoss,
            profitFactor: profitFactor,
            maxDrawdown: this.maxDrawdown,
            dailyTrades: this.dailyTrades,
            dailyProfit: this.dailyProfit,
            isInTrade: this.isInTrade,
            currentPosition: this.currentPosition
        };
    }

    /**
     * Calculate win rate percentage
     * @returns {number} Win rate percentage
     */
    getWinRate() {
        return this.totalTrades > 0 ? (this.winningTrades / this.totalTrades) * 100 : 0;
    }

    /**
     * Get recent trade history
     * @param {number} limit - Number of recent trades to return
     * @returns {Array} Recent trades
     */
    getRecentTrades(limit = 10) {
        return this.tradeHistory.slice(-limit).reverse();
    }

    /**
     * Reset trading engine (for testing)
     */
    reset() {
        this.currentBalance = this.initialBalance;
        this.currentPosition = null;
        this.isInTrade = false;
        this.tradeHistory = [];
        this.totalTrades = 0;
        this.winningTrades = 0;
        this.losingTrades = 0;
        this.totalProfit = 0;
        this.totalLoss = 0;
        this.maxDrawdown = 0;
        this.peakBalance = this.initialBalance;
        this.dailyTrades = 0;
        this.dailyProfit = 0;
        this.tradingStartTime = Date.now();
        
        console.log('🔄 Paper Trading Engine reset');
    }
}

module.exports = PaperTradingEngine;
