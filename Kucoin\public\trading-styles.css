/* Trading Dashboard Specific Styles */

/* Trading Stats Grid */
.stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.balance-card .stat-icon {
    background: linear-gradient(135deg, #00ff88, #00cc6a);
}

.trades-card .stat-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.winrate-card .stat-icon {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
}

.pnl-card .stat-icon {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.stat-content small {
    font-size: 0.8rem;
    opacity: 0.7;
    margin-top: 0.2rem;
    display: block;
}

/* Trading Grid Layout */
.trading-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.trading-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    overflow: hidden;
}

.card-header {
    padding: 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-header i {
    color: #ffd700;
}

.card-content {
    padding: 1.5rem;
}

/* Position Status */
.position-status .status-badge {
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.no-position {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.status-badge.in-position {
    background: linear-gradient(135deg, #00ff88, #00cc6a);
    color: #ffffff;
}

.status-badge.partial-position {
    background: linear-gradient(135deg, #ffa500, #ff8c00);
    color: #ffffff;
}

.status-badge.profit {
    background: linear-gradient(135deg, #00ff88, #00cc6a);
    color: #ffffff;
}

.status-badge.loss {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: #ffffff;
}

/* Position Details */
.position-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.position-item {
    text-align: center;
}

.position-item .label {
    font-size: 0.8rem;
    opacity: 0.7;
    margin-bottom: 0.2rem;
}

.position-item .value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffd700;
}

.position-item .value.profit {
    color: #00ff88;
}

.position-item .value.loss {
    color: #ff6b6b;
}

.no-position-message {
    text-align: center;
    padding: 2rem;
    opacity: 0.7;
}

.no-position-message i {
    font-size: 2rem;
    color: #ffd700;
    margin-bottom: 1rem;
}

/* Qualified Gainers */
.qualified-count {
    background: rgba(255, 215, 0, 0.2);
    color: #ffd700;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.qualified-list {
    max-height: 200px;
    overflow-y: auto;
}

.qualified-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.qualified-item:last-child {
    border-bottom: none;
}

.qualified-symbol {
    font-weight: 600;
    color: #ffffff;
}

.qualified-rank {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #1e3c72;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 700;
}

.qualified-time {
    font-size: 0.8rem;
    opacity: 0.7;
}

.loading-message {
    text-align: center;
    padding: 2rem;
    opacity: 0.7;
}

.loading-message i {
    font-size: 1.5rem;
    color: #ffd700;
    margin-bottom: 1rem;
}

/* Trades Section */
.trades-section, .performance-section {
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.section-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-header i {
    color: #ffd700;
}

.section-controls {
    display: flex;
    gap: 0.5rem;
}

/* Trades Table */
.trades-table-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    overflow: hidden;
}

.trades-table {
    width: 100%;
    border-collapse: collapse;
}

.trades-table th {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem 0.8rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.trades-table td {
    padding: 0.8rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
}

.trades-table tbody tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

.trade-id {
    font-weight: 700;
    color: #ffd700;
}

.trade-symbol {
    font-weight: 600;
    color: #ffffff;
}

.trade-price {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
}

.trade-pnl.profit {
    color: #00ff88;
    font-weight: 600;
}

.trade-pnl.loss {
    color: #ff6b6b;
    font-weight: 600;
}

.trade-percent.profit {
    color: #00ff88;
}

.trade-percent.loss {
    color: #ff6b6b;
}

.trade-duration {
    font-size: 0.8rem;
    opacity: 0.8;
}

.trade-reason {
    font-size: 0.8rem;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
}

.trade-time {
    font-size: 0.8rem;
    opacity: 0.7;
}

/* Performance Metrics */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.metric-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
}

.metric-card h4 {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    opacity: 0.8;
}

.metric-value {
    font-size: 1.3rem;
    font-weight: 700;
    color: #ffd700;
}

.metric-value.profit {
    color: #00ff88;
}

.metric-value.loss {
    color: #ff6b6b;
}

/* Responsive Design */
@media (max-width: 768px) {
    .trading-grid {
        grid-template-columns: 1fr;
    }
    
    .position-details {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .section-controls {
        justify-content: center;
    }
    
    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .trades-table {
        font-size: 0.8rem;
    }
    
    .trades-table th,
    .trades-table td {
        padding: 0.5rem 0.3rem;
    }
}

/* Animation for updates */
.stat-update {
    animation: statFlash 0.5s ease;
}

@keyframes statFlash {
    0% { background-color: rgba(255, 215, 0, 0.3); }
    100% { background-color: transparent; }
}

/* Trade row animation */
.new-trade {
    animation: newTradeSlide 0.5s ease;
}

@keyframes newTradeSlide {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Scrollbar Styling */
.qualified-list::-webkit-scrollbar,
.trades-table-container::-webkit-scrollbar {
    width: 6px;
}

.qualified-list::-webkit-scrollbar-track,
.trades-table-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.qualified-list::-webkit-scrollbar-thumb,
.trades-table-container::-webkit-scrollbar-thumb {
    background: rgba(255, 215, 0, 0.5);
    border-radius: 3px;
}

.qualified-list::-webkit-scrollbar-thumb:hover,
.trades-table-container::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 215, 0, 0.7);
}

/* Strategy Control Section */
.strategy-control-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.strategy-control-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.strategy-control-section h2 {
    color: #ffffff;
    font-size: 1.2rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-buttons {
    display: flex;
    gap: 10px;
}

.start-btn {
    background: linear-gradient(135deg, #00d4aa, #00b894);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
}

.start-btn:hover {
    background: linear-gradient(135deg, #00b894, #00a085);
    transform: translateY(-1px);
}

.start-btn:disabled {
    background: #666;
    cursor: not-allowed;
    opacity: 0.6;
}

.stop-btn {
    background: linear-gradient(135deg, #ffa500, #ff8c00);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
}

.stop-btn:hover {
    background: linear-gradient(135deg, #ff8c00, #ff7700);
    transform: translateY(-1px);
}

.stop-btn:disabled {
    background: #666;
    cursor: not-allowed;
    opacity: 0.6;
}

.reset-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
}

.reset-btn:hover {
    background: linear-gradient(135deg, #ee5a52, #dc4c41);
    transform: translateY(-1px);
}

.strategy-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.strategy-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    text-align: center;
}

.strategy-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.strategy-btn.active {
    background: linear-gradient(135deg, #00d4aa, #00b894);
    border-color: #00d4aa;
    box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
}

.strategy-btn i {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.strategy-btn span {
    font-weight: 600;
    font-size: 1rem;
}

.strategy-btn small {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-top: 2px;
}

/* Top Gainers Strategy Section */
.top-gainers-section {
    margin: 20px 0;
    padding: 20px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.top-gainers-section h3 {
    color: #ffffff;
    margin-bottom: 15px;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.gainers-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.gainers-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    text-align: center;
}

.gainers-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.gainers-btn.active {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    border-color: #ff6b6b;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.gainers-btn i {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.gainers-btn span {
    font-weight: 600;
    font-size: 1rem;
}

.gainers-btn small {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-top: 2px;
}

.gainers-info {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 10px 15px;
    border-left: 4px solid #ff6b6b;
    color: #ffffff;
    font-size: 0.9rem;
}

.strategy-info {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid #00d4aa;
}

.current-strategy {
    font-size: 1rem;
    margin-bottom: 8px;
    color: #ffffff;
}

.strategy-name {
    color: #00d4aa;
    font-weight: 600;
}

.strategy-description {
    font-size: 0.9rem;
    color: #b0b0b0;
    line-height: 1.4;
}
