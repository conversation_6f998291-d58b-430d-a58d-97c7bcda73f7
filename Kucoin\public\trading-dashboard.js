// Trading Dashboard JavaScript
class TradingDashboard {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.lastTradingData = null;
        this.currentBalance = 100;
        this.initialBalance = 100;
        
        this.init();
    }

    init() {
        this.connectWebSocket();
        this.setupEventListeners();
        this.updateConnectionStatus(false);
    }

    connectWebSocket() {
        this.socket = io();

        this.socket.on('connect', () => {
            this.isConnected = true;
            this.updateConnectionStatus(true);
            this.showToast('Connected to trading bot', 'success');
            // Check bot status when connected
            this.checkBotStatus();
        });

        this.socket.on('disconnect', () => {
            this.isConnected = false;
            this.updateConnectionStatus(false);
            this.showToast('Disconnected from trading bot', 'error');
        });

        this.socket.on('trading-update', (data) => {
            this.updateTradingData(data);
        });

        this.socket.on('gainers-update', (data) => {
            // Handle gainers data if needed
            this.updateLastUpdateTime(data.lastUpdate);
        });
    }

    setupEventListeners() {
        // Export trades function
        window.exportTrades = () => {
            this.exportTrades();
        };

        // Refresh trades function
        window.refreshTrades = () => {
            this.refreshData();
        };

        // Update time every second
        setInterval(() => {
            this.updateTimeDisplays();
        }, 1000);
    }

    updateConnectionStatus(connected) {
        const indicator = document.getElementById('connectionStatus');
        const text = document.getElementById('connectionText');
        
        if (connected) {
            indicator.classList.add('connected');
            text.textContent = 'Connected';
        } else {
            indicator.classList.remove('connected');
            text.textContent = 'Disconnected';
        }
    }

    updateTradingData(data) {
        this.lastTradingData = data;
        
        // Update balance and stats
        this.updateBalanceStats(data);
        
        // Update current position
        this.updateCurrentPosition(data.currentPosition);
        
        // Update qualified gainers (use momentum data if available, fallback to qualified gainers)
        this.updateQualifiedGainers(data.momentumData || data.qualifiedGainers || []);
        
        // Update recent trades
        this.updateRecentTrades(data.recentTrades || []);
        
        // Update performance metrics
        this.updatePerformanceMetrics(data.performanceMetrics);
        
        // Update last update time
        this.updateLastUpdateTime(data.lastUpdate);
    }

    updateBalanceStats(data) {
        const balance = data.balance || 100;
        const totalTrades = data.totalTrades || 0;
        const winRate = data.winRate || 0;
        const dailyProfit = data.dailyProfit || 0;
        
        this.currentBalance = balance;
        const totalReturn = ((balance - this.initialBalance) / this.initialBalance) * 100;
        
        // Update header stats
        document.getElementById('currentBalance').textContent = `$${balance.toFixed(2)}`;
        document.getElementById('totalReturn').textContent = `${totalReturn >= 0 ? '+' : ''}${totalReturn.toFixed(2)}%`;
        document.getElementById('totalReturn').className = totalReturn >= 0 ? 'profit' : 'loss';
        
        // Update stat cards
        document.getElementById('balanceAmount').textContent = `$${balance.toFixed(2)}`;
        document.getElementById('balanceChange').textContent = `${dailyProfit >= 0 ? '+' : ''}$${dailyProfit.toFixed(2)} today`;
        document.getElementById('balanceChange').className = dailyProfit >= 0 ? 'profit' : 'loss';
        
        document.getElementById('totalTrades').textContent = totalTrades;
        document.getElementById('dailyTrades').textContent = `${data.dailyTrades || 0} today`;
        
        document.getElementById('winRate').textContent = `${winRate.toFixed(1)}%`;
        document.getElementById('winLossRatio').textContent = `${data.winningTrades || 0}W / ${data.losingTrades || 0}L`;
        
        const netProfit = data.netProfit || 0;
        document.getElementById('totalPnL').textContent = `${netProfit >= 0 ? '+' : ''}$${netProfit.toFixed(2)}`;
        document.getElementById('totalPnL').className = netProfit >= 0 ? 'profit' : 'loss';
        document.getElementById('dailyPnL').textContent = `${dailyProfit >= 0 ? '+' : ''}$${dailyProfit.toFixed(2)} today`;
        document.getElementById('dailyPnL').className = dailyProfit >= 0 ? 'profit' : 'loss';
        
        // Add update animation
        this.addUpdateAnimation(['balanceAmount', 'totalTrades', 'winRate', 'totalPnL']);
    }

    updateCurrentPosition(position) {
        const statusElement = document.getElementById('positionStatus');
        const contentElement = document.getElementById('positionContent');
        
        if (!position) {
            // No position
            statusElement.innerHTML = '<span class="status-badge no-position">No Position</span>';
            contentElement.innerHTML = `
                <div class="no-position-message">
                    <i class="fas fa-search"></i>
                    <p>Scanning for entry signals...</p>
                </div>
            `;
        } else {
            // Active position (simple display)
            const currentValue = position.quantity * position.lastPrice;
            const unrealizedPnL = currentValue - position.positionValue;
            const unrealizedPercent = (unrealizedPnL / position.positionValue) * 100;
            const isProfit = unrealizedPnL >= 0;

            statusElement.innerHTML = `<span class="status-badge in-position">In Position</span>`;
            contentElement.innerHTML = `
                <div class="position-details">
                    <div class="position-item">
                        <div class="label">Symbol</div>
                        <div class="value">${position.symbol}</div>
                    </div>
                    <div class="position-item">
                        <div class="label">Entry Price</div>
                        <div class="value">$${position.entryPrice.toFixed(6)}</div>
                    </div>
                    <div class="position-item">
                        <div class="label">Current Price</div>
                        <div class="value">$${position.lastPrice.toFixed(6)}</div>
                    </div>
                    <div class="position-item">
                        <div class="label">Unrealized P&L</div>
                        <div class="value ${isProfit ? 'profit' : 'loss'}">
                            ${isProfit ? '+' : ''}$${unrealizedPnL.toFixed(4)} (${unrealizedPercent.toFixed(2)}%)
                        </div>
                    </div>
                    <div class="position-item">
                        <div class="label">Profit Target</div>
                        <div class="value">$${position.profitTarget.toFixed(6)} (1.20%)</div>
                    </div>
                    <div class="position-item">
                        <div class="label">Stop Loss</div>
                        <div class="value">$${position.stopLossPrice.toFixed(6)}</div>
                    </div>
                </div>
            `;
        }
    }

    updateQualifiedGainers(gainers) {
        const countElement = document.getElementById('qualifiedCount');
        const listElement = document.getElementById('qualifiedList');

        countElement.textContent = gainers.length;

        if (gainers.length === 0) {
            listElement.innerHTML = `
                <div class="loading-message">
                    <i class="fas fa-search"></i>
                    <p>No momentum data available</p>
                </div>
            `;
        } else {
            listElement.innerHTML = gainers.map(gainer => {
                // Handle both momentum data and qualified gainer data
                const symbol = gainer.symbol;
                const rank = gainer.rank;
                const velocity = gainer.velocity ? gainer.velocity.toFixed(2) : '0.00';
                const thrust = gainer.thrust ? gainer.thrust.toFixed(2) : '0.00';
                const velocitySignal = gainer.velocitySignal ? '🚀' : '';
                const thrustSignal = gainer.thrustSignal ? '🎯' : '';
                const timeDisplay = gainer.timeInGainers ? this.formatTimeAgo(gainer.timeInGainers) :
                                   `V:${velocity}% T:${thrust}%`;

                return `
                    <div class="qualified-item">
                        <div>
                            <div class="qualified-symbol">${symbol} ${velocitySignal}${thrustSignal}</div>
                            <div class="qualified-time">${timeDisplay}</div>
                        </div>
                        <div class="qualified-rank">#${rank}</div>
                    </div>
                `;
            }).join('');
        }
    }

    updateRecentTrades(trades) {
        const tbody = document.getElementById('tradesTableBody');
        
        if (trades.length === 0) {
            tbody.innerHTML = `
                <tr class="loading-row">
                    <td colspan="9">
                        <div class="loading-spinner">
                            <i class="fas fa-chart-line"></i>
                            No trades yet
                        </div>
                    </td>
                </tr>
            `;
        } else {
            tbody.innerHTML = trades.map(trade => {
                const isProfit = trade.netProfit > 0;
                const profitClass = isProfit ? 'profit' : 'loss';
                
                return `
                    <tr class="trade-row">
                        <td class="trade-id">#${trade.id}</td>
                        <td class="trade-symbol">${trade.symbol}</td>
                        <td class="trade-price">$${trade.entryPrice.toFixed(6)}</td>
                        <td class="trade-price">$${trade.exitPrice.toFixed(6)}</td>
                        <td class="trade-pnl ${profitClass}">
                            ${isProfit ? '+' : ''}$${trade.netProfit.toFixed(4)}
                        </td>
                        <td class="trade-percent ${profitClass}">
                            ${isProfit ? '+' : ''}${trade.profitPercent.toFixed(2)}%
                        </td>
                        <td class="trade-duration">${Math.round(trade.holdingTime / 1000)}s</td>
                        <td class="trade-reason">${trade.exitReason}</td>
                        <td class="trade-time">${new Date(trade.exitTime).toLocaleTimeString()}</td>
                    </tr>
                `;
            }).join('');
        }
    }

    updatePerformanceMetrics(metrics) {
        if (!metrics || !metrics.overview) return;
        
        const overview = metrics.overview;
        
        document.getElementById('profitFactor').textContent = overview.profitFactor.toFixed(2);
        document.getElementById('profitFactor').className = `metric-value ${overview.profitFactor >= 1 ? 'profit' : 'loss'}`;
        
        document.getElementById('avgWin').textContent = `$${overview.avgWin.toFixed(4)}`;
        document.getElementById('avgLoss').textContent = `$${overview.avgLoss.toFixed(4)}`;
        
        const maxDrawdown = metrics.drawdown ? metrics.drawdown.maxDrawdown : 0;
        document.getElementById('maxDrawdown').textContent = `${maxDrawdown.toFixed(2)}%`;
        document.getElementById('maxDrawdown').className = `metric-value ${maxDrawdown > 5 ? 'loss' : ''}`;
        
        const winStreak = metrics.streaks ? metrics.streaks.maxWinStreak : 0;
        document.getElementById('winStreak').textContent = winStreak;
        
        // Signals today - show velocity and thrust breakdown
        const totalSignals = this.lastTradingData?.totalSignals || 0;
        const velocitySignals = this.lastTradingData?.velocitySignals || 0;
        const thrustSignals = this.lastTradingData?.thrustSignals || 0;
        document.getElementById('signalsToday').textContent = `${totalSignals} (V:${velocitySignals} T:${thrustSignals})`;
    }

    updateLastUpdateTime(timestamp) {
        if (timestamp) {
            const timeAgo = this.formatTimeAgo(Date.now() - new Date(timestamp).getTime());
            document.getElementById('lastUpdate').textContent = timeAgo;
        }
    }

    updateTimeDisplays() {
        // Update any time-based displays that need regular updates
        if (this.lastTradingData && this.lastTradingData.lastUpdate) {
            this.updateLastUpdateTime(this.lastTradingData.lastUpdate);
        }
    }

    formatTimeAgo(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes % 60}m ago`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s ago`;
        } else {
            return `${seconds}s ago`;
        }
    }

    addUpdateAnimation(elementIds) {
        elementIds.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.classList.add('stat-update');
                setTimeout(() => element.classList.remove('stat-update'), 500);
            }
        });
    }

    refreshData() {
        if (this.socket && this.isConnected) {
            this.socket.emit('request-refresh');
            this.showToast('Refreshing data...', 'info');
        }
    }

    exportTrades() {
        // This would trigger a server-side export
        this.showToast('Export functionality coming soon...', 'info');
    }

    showToast(message, type = 'info') {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}"></i>
            ${message}
        `;
        
        container.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    async checkBotStatus() {
        try {
            const response = await fetch('/api/trading/status');
            if (response.ok) {
                const result = await response.json();
                if (result.success && result.data) {
                    // Assume bot is running if we get trading data
                    updateButtonStates(true);
                } else {
                    updateButtonStates(false);
                }
            } else {
                updateButtonStates(false);
            }
        } catch (error) {
            console.log('Could not check bot status:', error);
            updateButtonStates(false);
        }
    }
}

// Strategy Control Functions
async function setStrategy(strategy) {
    try {
        const response = await fetch('/api/strategy', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ strategy: strategy })
        });

        const result = await response.json();

        if (result.success) {
            // Update UI
            document.querySelectorAll('.strategy-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-strategy="${strategy}"]`).classList.add('active');

            // Update current strategy display
            document.getElementById('currentStrategy').textContent = strategy;

            // Update description
            const descriptions = {
                'VELOCITY': 'Testing 3-second velocity strategy (≥0.50% moves) - Higher quality signals',
                'THRUST': 'Testing 5-second thrust strategy (≥0.40% moves) - Higher quality signals'
            };
            document.getElementById('strategyDescription').textContent = descriptions[strategy];

            // Show success toast
            showToast(`Strategy changed to ${strategy}`, 'success');

            console.log(`✅ Strategy changed to: ${strategy}`);
        } else {
            showToast(`Failed to change strategy: ${result.error}`, 'error');
            console.error('❌ Failed to change strategy:', result.error);
        }
    } catch (error) {
        showToast('Error connecting to bot', 'error');
        console.error('❌ Error changing strategy:', error);
    }
}

// Top Gainers Strategy Control - DISABLED (Using fixed 24h strategy)
// async function setTopGainersStrategy(strategy) {
//     // Strategy switching disabled - using proven 24h system
//     console.log('Strategy switching disabled - using proven 24h price change system');
// }



async function startBot() {
    try {
        const response = await fetch('/api/start', {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            showToast('Bot started successfully!', 'success');
            updateButtonStates(true);
            console.log('✅ Bot started successfully');
        } else {
            showToast(`Failed to start bot: ${result.error}`, 'error');
            console.error('❌ Failed to start bot:', result.error);
        }
    } catch (error) {
        showToast('Error connecting to bot', 'error');
        console.error('❌ Error starting bot:', error);
    }
}

async function stopBot() {
    if (!confirm('Are you sure you want to stop the bot?\n\nThis will:\n• Stop all trading activity\n• Close any open positions\n• Keep current balance and history\n\nYou can restart it anytime.')) {
        return;
    }

    try {
        const response = await fetch('/api/stop', {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            showToast('Bot stopped successfully!', 'success');
            updateButtonStates(false);
            console.log('✅ Bot stopped successfully');
        } else {
            showToast(`Failed to stop bot: ${result.error}`, 'error');
            console.error('❌ Failed to stop bot:', result.error);
        }
    } catch (error) {
        showToast('Error connecting to bot', 'error');
        console.error('❌ Error stopping bot:', error);
    }
}

function updateButtonStates(isRunning) {
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');

    if (isRunning) {
        startBtn.disabled = true;
        stopBtn.disabled = false;
        startBtn.innerHTML = '<i class="fas fa-play"></i> Running...';
        stopBtn.innerHTML = '<i class="fas fa-stop"></i> Stop Bot';
    } else {
        startBtn.disabled = false;
        stopBtn.disabled = true;
        startBtn.innerHTML = '<i class="fas fa-play"></i> Start Bot';
        stopBtn.innerHTML = '<i class="fas fa-stop"></i> Stopped';
    }
}

async function resetBot() {
    if (!confirm('Are you sure you want to reset the bot? This will:\n\n• Reset balance to $100\n• Clear all trade history\n• Reset all statistics\n\nThis action cannot be undone.')) {
        return;
    }

    try {
        const response = await fetch('/api/reset', {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            // Show success message
            showToast('Bot reset successfully! Balance restored to $100', 'success');

            // Refresh the page to show clean state
            setTimeout(() => {
                window.location.reload();
            }, 1500);

            console.log('✅ Bot reset successfully');
        } else {
            showToast(`Failed to reset bot: ${result.error}`, 'error');
            console.error('❌ Failed to reset bot:', result.error);
        }
    } catch (error) {
        showToast('Error connecting to bot', 'error');
        console.error('❌ Error resetting bot:', error);
    }
}

// Helper function for toast notifications
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toastContainer');
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;

    const icon = type === 'success' ? 'check-circle' :
                 type === 'error' ? 'exclamation-circle' : 'info-circle';

    toast.innerHTML = `
        <i class="fas fa-${icon}"></i>
        <span>${message}</span>
    `;

    toastContainer.appendChild(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// Initialize trading dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    new TradingDashboard();
});
