/**
 * KuCoin API Configuration
 * 
 * IMPORTANT SECURITY NOTES:
 * - These credentials should be READ-ONLY
 * - Never commit this file to version control
 * - Keep your credentials secure
 */

module.exports = {
    // KuCoin API Credentials (READ-ONLY)
    kucoin: {
        apiKey: '687a98c1dffe710001e63aa3',
        apiSecret: 'bcb6ebcd-0bba-4fe3-9fe2-c9832450d2b8', 
        passphrase: 'Gainers',
        sandbox: false, // Set to true for sandbox testing
        
        // API Configuration
        baseURL: 'https://api.kucoin.com',
        timeout: 10000,
        
        // Rate Limiting (Authenticated limits are much higher)
        rateLimits: {
            public: 100,      // 100 requests per 10 seconds (public)
            private: 1000     // 1000 requests per 10 seconds (authenticated)
        }
    },
    
    // Trading Configuration
    trading: {
        paperTrading: true,  // Always keep as true for safety
        startingBalance: 100,
        
        // Risk Management
        maxPositionSize: 0.95,  // 95% of balance max
        stopLoss: 0.6,          // 0.6% stop loss
        profitTarget: 1.2       // 1.2% profit target
    },
    
    // Pressure Filter Configuration
    pressure: {
        enabled: true,
        threshold: 0.1,         // 10% buying pressure minimum
        updateInterval: 1000,   // 1 second updates
        cacheTimeout: 5000      // 5 second cache for orderbook data
    }
};
