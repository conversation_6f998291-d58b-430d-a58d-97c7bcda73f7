<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KuCoin Trading Bot V2 - Paper Trading Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="trading-styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-content">
                <div class="logo-section">
                    <i class="fas fa-robot"></i>
                    <h1>Trading Bot V2</h1>
                    <span class="version">Paper Trading</span>
                </div>
                <div class="status-section">
                    <div class="status-item">
                        <i class="fas fa-circle status-indicator" id="connectionStatus"></i>
                        <span id="connectionText">Connecting...</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-wallet"></i>
                        <span id="currentBalance">$100.00</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-chart-line"></i>
                        <span id="totalReturn">+0.00%</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-clock"></i>
                        <span id="lastUpdate">Never</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Trading Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card balance-card">
                    <div class="stat-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="balanceAmount">$100.00</h3>
                        <p>Current Balance</p>
                        <small id="balanceChange">+$0.00 today</small>
                    </div>
                </div>
                
                <div class="stat-card trades-card">
                    <div class="stat-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalTrades">0</h3>
                        <p>Total Trades</p>
                        <small id="dailyTrades">0 today</small>
                    </div>
                </div>
                
                <div class="stat-card winrate-card">
                    <div class="stat-icon">
                        <i class="fas fa-target"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="winRate">0%</h3>
                        <p>Win Rate</p>
                        <small id="winLossRatio">0W / 0L</small>
                    </div>
                </div>
                
                <div class="stat-card pnl-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalPnL">+$0.00</h3>
                        <p>Total P&L</p>
                        <small id="dailyPnL">+$0.00 today</small>
                    </div>
                </div>
            </div>

            <!-- Strategy Control Section -->
            <div class="strategy-control-section">
                <div class="section-header">
                    <h2><i class="fas fa-cogs"></i> Strategy Control</h2>
                    <div class="control-buttons">
                        <button class="start-btn" id="startBtn" onclick="startBot()">
                            <i class="fas fa-play"></i> Start Bot
                        </button>
                        <button class="stop-btn" id="stopBtn" onclick="stopBot()">
                            <i class="fas fa-stop"></i> Stop Bot
                        </button>
                        <button class="reset-btn" onclick="resetBot()">
                            <i class="fas fa-redo"></i> Reset Bot
                        </button>
                    </div>
                </div>
                <div class="strategy-buttons">
                    <button class="strategy-btn active" data-strategy="VELOCITY" onclick="setStrategy('VELOCITY')">
                        <i class="fas fa-rocket"></i>
                        <span>VELOCITY Strategy</span>
                        <small>3-sec ≥0.50%</small>
                    </button>
                    <button class="strategy-btn" data-strategy="THRUST" onclick="setStrategy('THRUST')">
                        <i class="fas fa-bolt"></i>
                        <span>THRUST Strategy</span>
                        <small>5-sec ≥0.40%</small>
                    </button>
                </div>

                <!-- Top Gainers Info -->
                <div class="top-gainers-section">
                    <h3><i class="fas fa-chart-line"></i> Top Gainers Strategy</h3>
                    <div class="strategy-info">
                        <div class="strategy-display">
                            <i class="fas fa-percentage"></i>
                            <span>24h Price Change (Proven System)</span>
                            <small>Traditional % gainers with 50% win rate</small>
                        </div>
                    </div>
                </div>

                <div class="strategy-info">
                    <div class="current-strategy">
                        <span>Active Strategy: </span>
                        <span id="currentStrategy" class="strategy-name">VELOCITY</span>
                    </div>
                    <div class="strategy-description" id="strategyDescription">
                        Testing 3-second velocity strategy (≥0.50% moves) - Higher quality signals
                    </div>
                </div>
            </div>

            <!-- Current Position & Qualified Gainers -->
            <div class="trading-grid">
                <!-- Current Position -->
                <div class="trading-card">
                    <div class="card-header">
                        <h3><i class="fas fa-crosshairs"></i> Current Position</h3>
                        <div class="position-status" id="positionStatus">
                            <span class="status-badge no-position">No Position</span>
                        </div>
                    </div>
                    <div class="card-content" id="positionContent">
                        <div class="no-position-message">
                            <i class="fas fa-search"></i>
                            <p>Scanning for entry signals...</p>
                        </div>
                    </div>
                </div>

                <!-- Qualified Gainers -->
                <div class="trading-card">
                    <div class="card-header">
                        <h3><i class="fas fa-star"></i> Qualified Gainers</h3>
                        <div class="qualified-count">
                            <span id="qualifiedCount">0</span> qualified
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="qualified-list" id="qualifiedList">
                            <div class="loading-message">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p>Loading qualified gainers...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Trades -->
            <div class="trades-section">
                <div class="section-header">
                    <h3><i class="fas fa-history"></i> Recent Trades</h3>
                    <div class="section-controls">
                        <button class="btn btn-secondary" onclick="exportTrades()">
                            <i class="fas fa-download"></i> Export
                        </button>
                        <button class="btn btn-primary" onclick="refreshTrades()">
                            <i class="fas fa-refresh"></i> Refresh
                        </button>
                    </div>
                </div>
                
                <div class="trades-table-container">
                    <table class="trades-table" id="tradesTable">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Symbol</th>
                                <th>Entry</th>
                                <th>Exit</th>
                                <th>P&L</th>
                                <th>%</th>
                                <th>Duration</th>
                                <th>Exit Reason</th>
                                <th>Time</th>
                            </tr>
                        </thead>
                        <tbody id="tradesTableBody">
                            <tr class="loading-row">
                                <td colspan="9">
                                    <div class="loading-spinner">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        Loading trades...
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="performance-section">
                <div class="section-header">
                    <h3><i class="fas fa-chart-bar"></i> Performance Metrics</h3>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <h4>Profit Factor</h4>
                        <div class="metric-value" id="profitFactor">0.00</div>
                    </div>
                    <div class="metric-card">
                        <h4>Avg Win</h4>
                        <div class="metric-value" id="avgWin">$0.00</div>
                    </div>
                    <div class="metric-card">
                        <h4>Avg Loss</h4>
                        <div class="metric-value" id="avgLoss">$0.00</div>
                    </div>
                    <div class="metric-card">
                        <h4>Max Drawdown</h4>
                        <div class="metric-value" id="maxDrawdown">0.00%</div>
                    </div>
                    <div class="metric-card">
                        <h4>Win Streak</h4>
                        <div class="metric-value" id="winStreak">0</div>
                    </div>
                    <div class="metric-card">
                        <h4>Signals Today</h4>
                        <div class="metric-value" id="signalsToday">0</div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="dashboard-footer">
            <div class="footer-content">
                <p>&copy; 2025 KuCoin Trading Bot V2 - Paper Trading Dashboard</p>
                <div class="footer-links">
                    <a href="/">View Gainers Dashboard</a>
                    <span>•</span>
                    <span>Paper Trading Mode</span>
                    <span>•</span>
                    <span>Real-time Updates</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <script src="trading-dashboard.js"></script>
</body>
</html>
