/**
 * Velocity-Based Entry Detector
 * Implements simple velocity entry strategy optimized for volatile top gainers:
 * 1. Single Velocity Check: 3-second velocity ≥ 0.35% (down from 0.50%)
 * 2. 5-Second Thrust: (Current - Lowest_of_5) / Lowest_of_5 ≥ 0.40%
 * 3. Quality Filters: 15% pressure + 8EMA + spread ≤0.13%
 */
class VelocityEntryDetector {
    constructor() {
        // Strategy parameters (simplified single velocity approach)
        this.VELOCITY_THRESHOLD = 0.0035; // 0.35% (single velocity threshold - 30% earlier than 0.50%)
        this.THRUST_THRESHOLD = 0.004; // 0.40% (higher quality signals)
        this.TOP_GAINERS_REQUIREMENT = 10; // Top 10 (broader than before)
        this.MIN_VOLUME_THRESHOLD = 200000; // $200K minimum volume
        this.MAX_HOURLY_GAIN = 0.30; // 30% max gain in 1 hour (avoid overextended)
        this.MIN_PRESSURE_THRESHOLD = 0.15; // 15% minimum buying pressure (increased)

        // Strategy toggle - can be changed via setStrategy()
        this.activeStrategy = 'VELOCITY'; // 'VELOCITY' or 'THRUST'
        
        // Price tracking for each symbol
        this.priceHistory = new Map(); // symbol -> array of {price, timestamp}
        this.volumeHistory = new Map(); // symbol -> array of {volume, timestamp}
        this.gainerRanks = new Map(); // symbol -> current rank in top gainers

        // EMA calculation for trend filter
        this.emaHistory = new Map(); // Store EMA values for each symbol
        this.emaPeriod = 8; // 8-period EMA for trend confirmation

        // Orderbook spread filter
        this.maxSpreadPercent = 0.13; // 0.13% maximum spread
        this.orderbookData = new Map(); // Store orderbook data for symbols
        
        // Signal tracking
        this.signalCooldowns = new Map(); // Prevent duplicate signals
        this.coinCooldowns = new Map(); // Prevent trading same coin immediately
        this.COIN_COOLDOWN_TIME = 180000; // 3 minutes = 180,000 ms
        this.totalSignals = 0;
        this.velocitySignals = 0;
        this.thrustSignals = 0;
        
        // Performance tracking
        this.lastSignalTime = 0;
        this.signalHistory = [];


        
        console.log('⚡ Velocity Entry Detector initialized');
        console.log(`🎯 Velocity Threshold: ${(this.VELOCITY_THRESHOLD * 100).toFixed(2)}%`);
        console.log(`🚀 Thrust Threshold: ${(this.THRUST_THRESHOLD * 100).toFixed(2)}%`);
        console.log(`📈 8EMA Trend Filter: ENABLED`);
        console.log(`📊 Orderbook Spread Filter: ≤${this.maxSpreadPercent}%`);
        console.log(`🔄 Active Strategy: ${this.activeStrategy}`);
    }

    /**
     * Calculate 5-period EMA for a symbol
     * @param {string} symbol - Symbol to calculate EMA for
     * @param {number} currentPrice - Current price
     * @returns {number|null} Current EMA value or null
     */
    calculateEMA(symbol, currentPrice) {
        if (!this.emaHistory.has(symbol)) {
            this.emaHistory.set(symbol, []);
        }

        const emaData = this.emaHistory.get(symbol);

        // For first price, EMA = price
        if (emaData.length === 0) {
            emaData.push(currentPrice);
            return currentPrice;
        }

        // EMA formula: EMA = (Price * (2/(n+1))) + (Previous EMA * (1 - (2/(n+1))))
        const multiplier = 2 / (this.emaPeriod + 1); // 2/(8+1) = 0.222
        const previousEMA = emaData[emaData.length - 1];
        const newEMA = (currentPrice * multiplier) + (previousEMA * (1 - multiplier));

        emaData.push(newEMA);

        // Keep only last 10 EMA values to save memory
        if (emaData.length > 10) {
            emaData.shift();
        }

        return newEMA;
    }

    /**
     * Get current EMA for a symbol
     * @param {string} symbol - Symbol to get EMA for
     * @returns {number|null} Current EMA value or null
     */
    getCurrentEMA(symbol) {
        const emaData = this.emaHistory.get(symbol);
        if (!emaData || emaData.length === 0) {
            return null;
        }
        return emaData[emaData.length - 1];
    }

    /**
     * Calculate bid-ask spread percentage
     * @param {string} symbol - Symbol to calculate spread for
     * @returns {number|null} Spread percentage or null if no data
     */
    calculateSpread(symbol) {
        const orderbook = this.orderbookData.get(symbol);
        if (!orderbook || !orderbook.bestBid || !orderbook.bestAsk) {
            return null;
        }

        const bid = parseFloat(orderbook.bestBid);
        const ask = parseFloat(orderbook.bestAsk);

        if (bid <= 0 || ask <= 0 || ask <= bid) {
            return null;
        }

        const spread = (ask - bid) / bid;
        return spread * 100; // Return as percentage
    }

    /**
     * Check if symbol passes spread filter
     * @param {string} symbol - Symbol to check
     * @returns {boolean} True if spread is acceptable
     */
    passesSpreadFilter(symbol) {
        const spreadPercent = this.calculateSpread(symbol);

        if (spreadPercent === null) {
            // No orderbook data - skip this symbol
            return false;
        }

        return spreadPercent <= this.maxSpreadPercent;
    }

    /**
     * Update price and volume data for all symbols
     * @param {Array} tickers - Current ticker data
     * @param {Array} topGainers - Current top gainers
     * @param {number} timestamp - Current timestamp
     * @param {Map} orderbooks - Orderbook data for symbols
     */
    updateMarketData(tickers, topGainers, timestamp = Date.now(), orderbooks = null) {
        // Clean up expired cooldowns
        this.cleanupCooldowns(timestamp);

        // Update orderbook data for spread filtering
        if (orderbooks) {
            this.orderbookData = orderbooks;
        }

        // Update gainer rankings
        this.updateGainerRanks(topGainers);
        
        // Update price and volume history
        tickers.forEach(ticker => {
            const symbol = ticker.symbol;
            const price = parseFloat(ticker.last);
            const volume = parseFloat(ticker.volValue || 0);
            
            if (isNaN(price) || isNaN(volume)) return;
            
            // Initialize history if not exists
            if (!this.priceHistory.has(symbol)) {
                this.priceHistory.set(symbol, []);
            }
            if (!this.volumeHistory.has(symbol)) {
                this.volumeHistory.set(symbol, []);
            }
            
            // Add current data point
            const priceHistory = this.priceHistory.get(symbol);
            const volumeHistory = this.volumeHistory.get(symbol);
            
            priceHistory.push({ price, timestamp });
            volumeHistory.push({ volume, timestamp });

            // Calculate and update EMA for trend filter
            this.calculateEMA(symbol, price);

            // Keep only last 10 seconds of data (for 1-second intervals)
            const cutoffTime = timestamp - 10000;
            this.priceHistory.set(symbol, priceHistory.filter(p => p.timestamp > cutoffTime));
            this.volumeHistory.set(symbol, volumeHistory.filter(v => v.timestamp > cutoffTime));
        });
        
        // Clean up old data
        this.cleanupOldData(timestamp);
    }

    /**
     * Update gainer rankings
     * @param {Array} topGainers - Current top gainers
     */
    updateGainerRanks(topGainers) {
        this.gainerRanks.clear();
        topGainers.forEach((gainer, index) => {
            this.gainerRanks.set(gainer.symbol, index + 1);
        });
    }

    /**
     * Detect entry signals using velocity and thrust strategies
     * @param {number} timestamp - Current timestamp
     * @returns {Array} Array of detected signals
     */
    detectEntrySignals(timestamp = Date.now()) {
        const signals = [];
        
        // Check each symbol that's in top gainers
        for (const [symbol, rank] of this.gainerRanks.entries()) {
            if (rank > this.TOP_GAINERS_REQUIREMENT) continue;
            
            // Check cooldown (prevent duplicate signals within 30 seconds)
            if (this.signalCooldowns.has(symbol)) {
                const lastSignal = this.signalCooldowns.get(symbol);
                if (timestamp - lastSignal < 30000) continue;
            }
            
            // Get price history for this symbol
            const priceHistory = this.priceHistory.get(symbol);
            if (!priceHistory || priceHistory.length < 6) continue; // Need at least 6 data points
            
            // Check basic filters first
            if (!this.passesBasicFilters(symbol, priceHistory)) continue;

            // Check coin cooldown - skip if coin was recently traded
            if (this.isInCooldown(symbol, timestamp)) continue;
            
            // Strategy 1: 3-Second Velocity
            if (this.activeStrategy === 'VELOCITY') {
                const velocitySignal = this.checkVelocitySignal(symbol, priceHistory, timestamp);
                if (velocitySignal) {
                    signals.push(velocitySignal);
                    this.velocitySignals++;
                }
            }

            // Strategy 2: 5-Second Thrust
            if (this.activeStrategy === 'THRUST') {
                const thrustSignal = this.checkThrustSignal(symbol, priceHistory, timestamp);
                if (thrustSignal) {
                    signals.push(thrustSignal);
                    this.thrustSignals++;
                }
            }


        }
        
        // Process and log signals
        signals.forEach(signal => {
            this.signalCooldowns.set(signal.symbol, timestamp);
            this.totalSignals++;
            this.signalHistory.push(signal);
            this.logSignal(signal);
        });
        
        return signals;
    }

    /**
     * Check simple velocity signal (optimized for volatile top gainers)
     * @param {string} symbol - Symbol to check
     * @param {Array} priceHistory - Price history array
     * @param {number} timestamp - Current timestamp
     * @returns {Object|null} Signal object or null
     */
    checkVelocitySignal(symbol, priceHistory, timestamp) {
        // Need at least 4 data points for 3-second velocity
        if (priceHistory.length < 4) return null;

        // Get last 4 prices (M₀, M₁, M₂, M₃) where M₀ is most recent
        const recentPrices = priceHistory.slice(-4);
        const M0 = recentPrices[3].price; // Most recent (current)
        const M2 = recentPrices[1].price; // 2 seconds ago

        // Calculate 3-second velocity: VS = (M₀ - M₂) / M₂
        const velocity = (M0 - M2) / M2;

        // Calculate pressure for all velocity checks
        const pressure = this.calculateOrderBookPressure(symbol);

        // Check if simple velocity threshold met
        if (velocity >= this.VELOCITY_THRESHOLD) {
            // CRITICAL: Order Book Pressure Filter - only trade with buying pressure ≥ 15%
            if (pressure === null || pressure < this.MIN_PRESSURE_THRESHOLD) {
                // No pressure data or insufficient buying pressure - skip this signal
                return null;
            }

            // CRITICAL: 8EMA Trend Filter - only trade when price is above 8EMA
            const currentEMA = this.getCurrentEMA(symbol);
            if (currentEMA && M0 <= currentEMA) {
                // Price is below 8EMA - skip this signal (counter-trend)
                return null;
            }

            // CRITICAL: Orderbook Spread Filter - only trade tight spreads
            if (!this.passesSpreadFilter(symbol)) {
                // Spread too wide - skip this signal
                return null;
            }

            // Additional confirmation: check volume
            if (!this.checkVolumeConfirmation(symbol, timestamp)) return null;

            return {
                symbol: symbol,
                type: 'VELOCITY',
                entryPrice: M0,
                velocity: velocity,
                velocityPercent: velocity * 100,
                pressure: pressure,
                pressurePercent: pressure * 100,
                timestamp: timestamp,
                rank: this.gainerRanks.get(symbol),
                confidence: this.calculateVelocityConfidence(velocity, symbol),
                priceData: {
                    M0: M0,
                    M2: M2,
                    change: M0 - M2,
                    ema8: currentEMA,
                    spread: this.calculateSpread(symbol),
                    pressure: pressure
                }
            };
        }
        
        return null;
    }

    /**
     * Check 5-second thrust signal
     * @param {string} symbol - Symbol to check
     * @param {Array} priceHistory - Price history array
     * @param {number} timestamp - Current timestamp
     * @returns {Object|null} Signal object or null
     */
    checkThrustSignal(symbol, priceHistory, timestamp) {
        // Need at least 5 data points for 5-second thrust
        if (priceHistory.length < 5) return null;
        
        // Get last 5 prices
        const last5Prices = priceHistory.slice(-5);
        const currentPrice = last5Prices[4].price; // Most recent
        const lowest5 = Math.min(...last5Prices.map(p => p.price));
        
        // Calculate thrust: (Current - Lowest_of_5) / Lowest_of_5
        const thrust = (currentPrice - lowest5) / lowest5;
        
        // Check if thrust meets threshold
        if (thrust >= this.THRUST_THRESHOLD) {
            // CRITICAL: 8EMA Trend Filter - only trade when price is above 8EMA
            const currentEMA = this.getCurrentEMA(symbol);
            if (currentEMA && currentPrice <= currentEMA) {
                // Price is below 8EMA - skip this signal (counter-trend)
                return null;
            }

            // CRITICAL: Orderbook Spread Filter - only trade tight spreads
            if (!this.passesSpreadFilter(symbol)) {
                // Spread too wide - skip this signal
                return null;
            }

            // Additional confirmation: check volume
            if (!this.checkVolumeConfirmation(symbol, timestamp)) return null;

            return {
                symbol: symbol,
                type: 'THRUST',
                entryPrice: currentPrice,
                thrust: thrust,
                thrustPercent: thrust * 100,
                timestamp: timestamp,
                rank: this.gainerRanks.get(symbol),
                confidence: this.calculateThrustConfidence(thrust, symbol),
                priceData: {
                    current: currentPrice,
                    lowest5: lowest5,
                    change: currentPrice - lowest5,
                    ema8: currentEMA,
                    spread: this.calculateSpread(symbol)
                }
            };
        }
        
        return null;
    }

    /**
     * Check basic filters for a symbol
     * @param {string} symbol - Symbol to check
     * @param {Array} priceHistory - Price history
     * @returns {boolean} True if passes filters
     */
    passesBasicFilters(symbol, priceHistory) {
        // Check minimum volume
        const volumeHistory = this.volumeHistory.get(symbol);
        if (!volumeHistory || volumeHistory.length === 0) return false;
        
        const latestVolume = volumeHistory[volumeHistory.length - 1].volume;
        if (latestVolume < this.MIN_VOLUME_THRESHOLD) return false;
        
        // Check if not overextended (avoid coins up >30% in 1 hour)
        if (priceHistory.length >= 2) {
            const currentPrice = priceHistory[priceHistory.length - 1].price;
            const oldestPrice = priceHistory[0].price;
            const hourlyGain = (currentPrice - oldestPrice) / oldestPrice;
            
            if (hourlyGain > this.MAX_HOURLY_GAIN) return false;
        }
        
        return true;
    }

    /**
     * Check volume confirmation
     * @param {string} symbol - Symbol to check
     * @param {number} timestamp - Current timestamp
     * @returns {boolean} True if volume confirms
     */
    checkVolumeConfirmation(symbol, timestamp) {
        const volumeHistory = this.volumeHistory.get(symbol);
        if (!volumeHistory || volumeHistory.length < 3) return false;
        
        // Check if recent volume is above average
        const recentVolumes = volumeHistory.slice(-3);
        const avgVolume = recentVolumes.reduce((sum, v) => sum + v.volume, 0) / recentVolumes.length;
        const currentVolume = recentVolumes[recentVolumes.length - 1].volume;
        
        return currentVolume > avgVolume * 0.8; // Allow 80% of average (not too strict)
    }

    /**
     * Calculate confidence for simple velocity signal
     * @param {number} velocity - Velocity value
     * @param {string} symbol - Symbol
     * @returns {number} Confidence score (0-100)
     */
    calculateVelocityConfidence(velocity, symbol) {
        let confidence = 65; // Base confidence for 0.35% threshold

        // Velocity strength bonus (adjusted for 0.35% threshold)
        if (velocity > 0.008) confidence += 25; // >0.8%
        else if (velocity > 0.006) confidence += 20; // >0.6%
        else if (velocity > 0.005) confidence += 15; // >0.5%
        else if (velocity > 0.004) confidence += 10; // >0.4%
        else if (velocity > 0.0035) confidence += 5; // >0.35%

        // Rank bonus
        const rank = this.gainerRanks.get(symbol) || 11;
        if (rank <= 3) confidence += 15;
        else if (rank <= 5) confidence += 10;
        else if (rank <= 7) confidence += 5;

        return Math.min(confidence, 100);
    }

    calculateThrustConfidence(thrust, symbol) {
        let confidence = 55; // Base confidence (slightly lower than velocity)

        // Thrust strength bonus
        if (thrust > 0.004) confidence += 20; // >0.4%
        else if (thrust > 0.003) confidence += 15; // >0.3%
        else if (thrust > 0.002) confidence += 10; // >0.2%

        // Rank bonus
        const rank = this.gainerRanks.get(symbol) || 11;
        if (rank <= 3) confidence += 15;
        else if (rank <= 5) confidence += 10;
        else if (rank <= 7) confidence += 5;

        return Math.min(confidence, 100);
    }

    /**
     * Calculate order book pressure within 0.2% of mid price
     * @param {string} symbol - Symbol to calculate for
     * @returns {number|null} Pressure value (-1 to +1) or null
     */
    calculateOrderBookPressure(symbol) {
        const orderbook = this.orderbookData.get(symbol);
        if (!orderbook || !orderbook.bids || !orderbook.asks) return null;

        // Calculate mid price
        const bestBid = parseFloat(orderbook.bids[0]?.[0]) || 0;
        const bestAsk = parseFloat(orderbook.asks[0]?.[0]) || 0;
        if (bestBid === 0 || bestAsk === 0) return null;

        const midPrice = (bestBid + bestAsk) / 2;
        const pressureRange = midPrice * 0.002; // 0.2% of mid price

        // Sum bid and ask volumes within 0.2% of mid
        let bidSum = 0;
        let askSum = 0;

        // Sum bids within range
        for (const [price, size] of orderbook.bids) {
            const bidPrice = parseFloat(price);
            if (bidPrice >= midPrice - pressureRange) {
                bidSum += parseFloat(size) * bidPrice; // Volume in USDT
            } else {
                break; // Bids are sorted descending
            }
        }

        // Sum asks within range
        for (const [price, size] of orderbook.asks) {
            const askPrice = parseFloat(price);
            if (askPrice <= midPrice + pressureRange) {
                askSum += parseFloat(size) * askPrice; // Volume in USDT
            } else {
                break; // Asks are sorted ascending
            }
        }

        if (bidSum + askSum === 0) return null;

        // Calculate pressure: P = (bid_sum - ask_sum) / (bid_sum + ask_sum)
        const pressure = (bidSum - askSum) / (bidSum + askSum);

        return pressure;
    }









    /**
     * Log detected signal
     * @param {Object} signal - Signal object
     */
    logSignal(signal) {
        console.log(`\n⚡ ${signal.type} SIGNAL DETECTED:`);
        console.log(`   Symbol: ${signal.symbol} (Rank #${signal.rank})`);
        console.log(`   Entry Price: $${signal.entryPrice.toFixed(6)}`);
        
        if (signal.type === 'VELOCITY') {
            console.log(`   3s Velocity: ${signal.velocityPercent.toFixed(3)}%`);
            console.log(`   💪 Pressure: ${signal.pressurePercent.toFixed(1)}% (≥10% ✅)`);
            console.log(`   Price Change: $${signal.priceData.M0.toFixed(6)} - $${signal.priceData.M2.toFixed(6)} = $${signal.priceData.change.toFixed(6)}`);
            if (signal.priceData.ema8) {
                console.log(`   📈 8EMA: $${signal.priceData.ema8.toFixed(6)} (Price above EMA ✅)`);
            }
            if (signal.priceData.spread !== null) {
                console.log(`   📊 Spread: ${signal.priceData.spread.toFixed(3)}% (≤0.13% ✅)`);
            }
        } else if (signal.type === 'THRUST') {
            console.log(`   5s Thrust: ${signal.thrustPercent.toFixed(3)}%`);
            console.log(`   Price Change: $${signal.priceData.current.toFixed(6)} - $${signal.priceData.lowest5.toFixed(6)} = $${signal.priceData.change.toFixed(6)}`);
            if (signal.priceData.ema8) {
                console.log(`   📈 8EMA: $${signal.priceData.ema8.toFixed(6)} (Price above EMA ✅)`);
            }
            if (signal.priceData.spread !== null) {
                console.log(`   📊 Spread: ${signal.priceData.spread.toFixed(3)}% (≤0.13% ✅)`);
            }
        }
        
        console.log(`   Confidence: ${signal.confidence.toFixed(1)}%`);
    }

    /**
     * Check if a coin is in cooldown period
     * @param {string} symbol - Symbol to check
     * @param {number} timestamp - Current timestamp
     * @returns {boolean} True if in cooldown
     */
    isInCooldown(symbol, timestamp) {
        const cooldownEnd = this.coinCooldowns.get(symbol);
        if (!cooldownEnd) return false;

        return timestamp < cooldownEnd;
    }

    /**
     * Add a coin to cooldown after trading
     * @param {string} symbol - Symbol to add to cooldown
     * @param {number} timestamp - Trade timestamp
     */
    addToCooldown(symbol, timestamp = Date.now()) {
        const cooldownEnd = timestamp + this.COIN_COOLDOWN_TIME;
        this.coinCooldowns.set(symbol, cooldownEnd);

        console.log(`🚫 ${symbol} added to cooldown for 3 minutes`);
    }

    /**
     * Clean up expired cooldowns
     * @param {number} timestamp - Current timestamp
     */
    cleanupCooldowns(timestamp) {
        for (const [symbol, cooldownEnd] of this.coinCooldowns.entries()) {
            if (timestamp >= cooldownEnd) {
                this.coinCooldowns.delete(symbol);
                console.log(`✅ ${symbol} cooldown expired - available for trading`);
            }
        }
    }

    /**
     * Clean up old data
     * @param {number} timestamp - Current timestamp
     */
    cleanupOldData(timestamp) {
        const cutoffTime = timestamp - 300000; // 5 minutes
        
        // Clean up signal cooldowns
        for (const [symbol, lastSignal] of this.signalCooldowns.entries()) {
            if (timestamp - lastSignal > 60000) { // 1 minute
                this.signalCooldowns.delete(symbol);
            }
        }
        
        // Keep signal history manageable
        if (this.signalHistory.length > 100) {
            this.signalHistory = this.signalHistory.slice(-50);
        }
    }

    /**
     * Get symbols that qualify for velocity/thrust signals (before pressure filter)
     * @returns {Array<string>} Array of qualified symbol names
     */
    getQualifiedSymbols() {
        const qualifiedSymbols = [];
        const timestamp = Date.now();

        for (const [symbol, priceHistory] of this.priceHistory) {
            // Skip if not in top gainers
            if (!this.gainerRanks.has(symbol)) continue;

            // Skip if in cooldown
            if (this.isInCooldown(symbol, timestamp)) continue;

            // Check if symbol would qualify for velocity signal (without pressure filter)
            if (this.activeStrategy === 'VELOCITY') {
                if (this.wouldQualifyForVelocity(symbol, priceHistory)) {
                    qualifiedSymbols.push(symbol);
                }
            }

            // Check if symbol would qualify for thrust signal (without pressure filter)
            if (this.activeStrategy === 'THRUST') {
                if (this.wouldQualifyForThrust(symbol, priceHistory)) {
                    qualifiedSymbols.push(symbol);
                }
            }
        }

        return qualifiedSymbols;
    }

    /**
     * Check if symbol would qualify for velocity signal (without pressure filter)
     * @param {string} symbol - Symbol to check
     * @param {Array} priceHistory - Price history array
     * @returns {boolean} True if would qualify
     */
    wouldQualifyForVelocity(symbol, priceHistory) {
        if (priceHistory.length < 4) return false;

        const recentPrices = priceHistory.slice(-4);
        const M0 = recentPrices[3].price;
        const M2 = recentPrices[1].price;
        const velocity = (M0 - M2) / M2;

        if (velocity < this.VELOCITY_THRESHOLD) return false;

        // Check EMA filter
        const currentEMA = this.getCurrentEMA(symbol);
        if (currentEMA && M0 <= currentEMA) return false;

        return true;
    }

    /**
     * Check if symbol would qualify for thrust signal (without pressure filter)
     * @param {string} symbol - Symbol to check
     * @param {Array} priceHistory - Price history array
     * @returns {boolean} True if would qualify
     */
    wouldQualifyForThrust(symbol, priceHistory) {
        if (priceHistory.length < 5) return false;

        const last5Prices = priceHistory.slice(-5);
        const M0 = last5Prices[4].price;
        const M4 = last5Prices[0].price;
        const thrust = (M0 - M4) / M4;

        if (thrust < this.THRUST_THRESHOLD) return false;

        // Check EMA filter
        const currentEMA = this.getCurrentEMA(symbol);
        if (currentEMA && M0 <= currentEMA) return false;

        return true;
    }

    /**
     * Get detector statistics
     * @returns {Object} Statistics object
     */
    getStatistics() {
        return {
            totalSignals: this.totalSignals,
            velocitySignals: this.velocitySignals,
            thrustSignals: this.thrustSignals,
            trackedSymbols: this.priceHistory.size,
            topGainersTracked: this.gainerRanks.size,
            recentSignals: this.signalHistory.slice(-10),
            signalRate: {
                velocity: this.totalSignals > 0 ? (this.velocitySignals / this.totalSignals) * 100 : 0,
                thrust: this.totalSignals > 0 ? (this.thrustSignals / this.totalSignals) * 100 : 0
            }
        };
    }

    /**
     * Get current market momentum data
     * @returns {Object} Momentum data for dashboard
     */
    getMomentumData() {
        const momentumData = [];
        
        for (const [symbol, rank] of this.gainerRanks.entries()) {
            if (rank > 10) continue;
            
            const priceHistory = this.priceHistory.get(symbol);
            if (!priceHistory || priceHistory.length < 5) continue;
            
            // Calculate current velocity and thrust
            const last4 = priceHistory.slice(-4);
            const last5 = priceHistory.slice(-5);
            
            let velocity = 0;
            let thrust = 0;
            
            if (last4.length >= 4) {
                const M0 = last4[3].price;
                const M2 = last4[1].price;
                velocity = (M0 - M2) / M2;
            }
            
            if (last5.length >= 5) {
                const current = last5[4].price;
                const lowest = Math.min(...last5.map(p => p.price));
                thrust = (current - lowest) / lowest;
            }
            
            momentumData.push({
                symbol: symbol,
                rank: rank,
                velocity: velocity * 100,
                thrust: thrust * 100,
                velocitySignal: velocity >= this.VELOCITY_THRESHOLD,
                thrustSignal: thrust >= this.THRUST_THRESHOLD
            });
        }
        
        return momentumData.sort((a, b) => a.rank - b.rank);
    }

    /**
     * Set active trading strategy
     * @param {string} strategy - 'VELOCITY' or 'THRUST'
     */
    setStrategy(strategy) {
        const validStrategies = ['VELOCITY', 'THRUST'];
        if (!validStrategies.includes(strategy)) {
            console.error(`❌ Invalid strategy: ${strategy}. Valid options: ${validStrategies.join(', ')}`);
            return false;
        }

        this.activeStrategy = strategy;
        console.log(`🔄 Strategy changed to: ${strategy}`);

        // Reset signal counters when changing strategy
        this.totalSignals = 0;
        this.velocitySignals = 0;
        this.thrustSignals = 0;

        return true;
    }

    /**
     * Get current active strategy
     * @returns {string} Current strategy
     */
    getActiveStrategy() {
        return this.activeStrategy;
    }

    /**
     * Reset all statistics and data
     */
    reset() {
        this.priceHistory.clear();
        this.volumeHistory.clear();
        this.gainerRanks.clear();
        this.signalCooldowns.clear();
        this.totalSignals = 0;
        this.velocitySignals = 0;
        this.thrustSignals = 0;
        this.signalHistory = [];

        console.log('🔄 Velocity detector reset - all data cleared');
    }
}

module.exports = VelocityEntryDetector;
