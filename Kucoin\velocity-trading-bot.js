#!/usr/bin/env node

/**
 * KuCoin Velocity Trading Bot V2.1 - Aggressive Paper Trading
 * Enhanced trading bot with velocity and thrust-based entry detection
 * 
 * New Features:
 * - 3-Second Velocity Strategy: VS = (M₀ - M₂) / M₂ ≥ 0.30%
 * - 5-Second Thrust Strategy: (Current - Lowest_of_5) / Lowest_of_5 ≥ 0.20%
 * - Much faster signal detection (1-5 seconds vs 3+ minutes)
 * - Higher trade frequency targeting 10-15+ trades daily
 * - Same exit conditions: 0.56% profit / 0.15% stop loss
 */

const KuCoinAPI = require('./src/kucoin-api');
const DataProcessor = require('./src/data-processor');
const ErrorHandler = require('./src/error-handler');
const WebServer = require('./src/web-server');
const PaperTradingEngine = require('./src/paper-trading-engine');
const VelocityEntryDetector = require('./src/velocity-entry-detector');
const VelocityPositionManager = require('./src/velocity-position-manager');
const TradeLogger = require('./src/trade-logger');

// Load API configuration
let apiConfig = null;
try {
    apiConfig = require('./config/api-config');
    console.log('✅ API configuration loaded');
} catch (error) {
    console.log('⚠️ No API config found - using public endpoints only');
}

class VelocityTradingBot {
    constructor() {
        // Core components
        this.api = new KuCoinAPI(apiConfig);
        this.dataProcessor = new DataProcessor();
        this.errorHandler = new ErrorHandler();
        this.webServer = new WebServer(3002, this); // Use port 3002 for velocity bot
        
        // Trading components (new velocity-based system)
        this.tradingEngine = new PaperTradingEngine();
        this.velocityDetector = new VelocityEntryDetector();
        this.positionManager = new VelocityPositionManager(this.tradingEngine, this.velocityDetector);
        this.tradeLogger = new TradeLogger();
        
        // Data and state
        this.currentTopGainers = [];
        this.currentTickers = [];
        this.isRunning = false;
        this.lastUpdate = null;
        
        // Faster update intervals for velocity trading
        this.DATA_UPDATE_INTERVAL = 1000; // 1 second for faster data
        this.TRADING_UPDATE_INTERVAL = 500; // 0.5 seconds for trading logic
        this.DASHBOARD_UPDATE_INTERVAL = 1000; // 1 second for dashboard

        // Top gainers strategy selection
        this.topGainersStrategy = 'PRICE_24H'; // Use proven 24h strategy
        
        // Timers
        this.dataUpdateTimer = null;
        this.tradingUpdateTimer = null;
        this.dashboardUpdateTimer = null;

        // Bot state
        this.botRunning = false;
        
        console.log('⚡ KuCoin Velocity Trading Bot V2.1 initialized');
        console.log('🚀 Aggressive Paper Trading - Velocity & Thrust Strategies');
        console.log('💰 Starting Balance: $100 USDT');
    }

    /**
     * Start the velocity trading bot
     */
    async start() {
        try {
            console.log('🚀 Starting KuCoin Velocity Trading Bot V2.1...');
            
            // Test API connection
            const isConnected = await this.api.testConnection();
            if (!isConnected) {
                throw new Error('Failed to connect to KuCoin API');
            }
            console.log('✅ KuCoin API connection established');
            
            // Start web server
            await this.webServer.start();
            console.log('🌐 Velocity trading dashboard available at: http://localhost:3002/trading');

            // Initial data fetch
            await this.updateMarketData();
            console.log('📊 Initial market data loaded');
            
            // Start position monitoring
            this.positionManager.startMonitoring(this.TRADING_UPDATE_INTERVAL);
            console.log('👁️ Velocity position monitoring started');
            
            // Start update timers
            this.startUpdateTimers();
            
            this.isRunning = true;
            this.botRunning = true;
        console.log('✅ Velocity Trading Bot V2.1 started successfully!');
            console.log('⚡ Ready to detect velocity and thrust signals');
            console.log('📊 Top Gainers Strategy: 24h Price Change (Proven System)');
            console.log('🎯 Target: 10-15+ trades daily with high win rate');
            
            // Log initial status
            this.logStatus();
            
        } catch (error) {
            const shouldContinue = this.errorHandler.handleError(error, 'Velocity Bot Startup', true);
            if (!shouldContinue) {
                console.error('❌ Failed to start velocity trading bot - terminating');
                process.exit(1);
            }
        }
    }

    /**
     * Start all update timers with faster intervals
     */
    startUpdateTimers() {
        // Data update timer (fetch market data every second)
        this.dataUpdateTimer = setInterval(() => {
            if (this.botRunning) {
                this.updateMarketData();
            }
        }, this.DATA_UPDATE_INTERVAL);

        // Dashboard update timer (update web dashboard)
        this.dashboardUpdateTimer = setInterval(() => {
            if (this.botRunning) {
                this.updateDashboard();
            }
        }, this.DASHBOARD_UPDATE_INTERVAL);

        console.log('⏰ Velocity update timers started (1s data, 1s dashboard)');
    }

    /**
     * Update market data from KuCoin API
     */
    async updateMarketData() {
        const wrappedUpdate = this.errorHandler.wrapAsync(async () => {
            // Fetch all tickers
            const fetchTickers = this.errorHandler.withRetry(
                () => this.api.getAllTickers(),
                2, // Fewer retries for faster updates
                1000
            );

            const tickers = await fetchTickers();
            if (!tickers) {
                throw new Error('Failed to fetch tickers after retries');
            }

            // Process data to get top gainers using proven 24h strategy
            this.currentTopGainers = this.dataProcessor.getTopGainers(tickers, 15);
            this.currentTickers = tickers;
            this.lastUpdate = Date.now();

            // Fetch detailed orderbook data for top gainers (for spread filtering AND pressure calculation)
            const topGainerSymbols = this.currentTopGainers.map(g => g.symbol);
            const orderbooks = await this.api.getBatchOrderbooks(topGainerSymbols, true); // true = detailed orderbooks

            // Update trading components with new data
            this.positionManager.updateMarketData(
                this.currentTopGainers,
                this.currentTickers,
                this.lastUpdate,
                orderbooks
            );

            // Only reset error counter every 10 successful updates to reduce spam
            if (Date.now() % 10000 < 1000) {
                this.errorHandler.reset();
            }
            
        }, 'Update Market Data', false);
        
        await wrappedUpdate();
    }

    /**
     * Update web dashboard with current data
     */
    updateDashboard() {
        try {
            // Get trading statistics
            const tradingStats = this.positionManager.getStatistics();
            const loggerStats = this.tradeLogger.getStatistics();
            
            // Get velocity detector stats for VAP signals
            const velocityStats = this.positionManager.velocityDetector.getStatistics();

            // Prepare dashboard data
            const dashboardData = {
                // Market data
                topGainers: this.currentTopGainers,
                lastUpdate: this.lastUpdate,
                timestamp: new Date().toISOString(),

                // Trading data
                currentPosition: this.positionManager.getCurrentPosition(),
                momentumData: this.positionManager.getMomentumData(),

                // Statistics
                balance: tradingStats.currentBalance,
                totalTrades: tradingStats.totalTrades,
                winRate: tradingStats.winRate,
                dailyProfit: tradingStats.dailyProfit,
                isInTrade: tradingStats.isInTrade,

                // Signal stats (including VAP)
                velocityTrades: tradingStats.velocityTrades,
                thrustTrades: tradingStats.thrustTrades,
                totalSignals: velocityStats.totalSignals,
                velocitySignals: velocityStats.velocitySignals,
                thrustSignals: velocityStats.thrustSignals,
                signalAccuracy: tradingStats.signalAccuracy,

                // Strategy info
                activeStrategy: this.positionManager.velocityDetector.activeStrategy,

                // Recent trades
                recentTrades: this.tradeLogger.getRecentTrades(10),

                // Performance metrics
                performanceMetrics: loggerStats.performanceMetrics
            };
            
            // Broadcast to web dashboard (use same event name as original)
            this.webServer.io.emit('trading-update', dashboardData);
            
        } catch (error) {
            console.error('❌ Error updating velocity dashboard:', error.message);
        }
    }

    /**
     * Log current bot status
     */
    logStatus() {
        const stats = this.positionManager.getStatistics();
        
        console.log('\n📊 VELOCITY TRADING BOT STATUS:');
        console.log(`   💰 Balance: $${stats.currentBalance.toFixed(2)}`);
        console.log(`   📈 Total Trades: ${stats.totalTrades}`);
        console.log(`   🎯 Win Rate: ${stats.winRate.toFixed(1)}%`);
        console.log(`   💵 Daily P&L: ${stats.dailyProfit > 0 ? '+' : ''}$${stats.dailyProfit.toFixed(2)}`);
        console.log(`   🔄 In Trade: ${stats.isInTrade ? 'YES' : 'NO'}`);
        console.log(`   ⚡ Total Signals: ${stats.totalSignals}`);
        console.log(`   🚀 Velocity Signals: ${stats.velocitySignals}`);
        console.log(`   🎯 Thrust Signals: ${stats.thrustSignals}`);

        console.log(`   📊 Signal Accuracy: ${stats.signalAccuracy.toFixed(1)}%`);
        console.log(`   🏆 Top Gainers Tracked: ${stats.topGainersTracked}`);
        
        if (stats.isInTrade) {
            const position = this.positionManager.getCurrentPosition();
            console.log(`   📍 Current Position: ${position.symbol} @ $${position.entryPrice.toFixed(6)} (${position.entryType || 'UNKNOWN'})`);
        }
    }

    /**
     * Handle trade completion (called by position manager)
     * @param {Object} trade - Completed trade
     * @param {Object} signal - Original entry signal
     */
    onTradeCompleted(trade, signal) {
        // Log trade with comprehensive data
        this.tradeLogger.logTrade(trade, signal, {
            topGainersAtEntry: this.currentTopGainers.slice(0, 5),
            marketCondition: 'velocity_trading',
            signalType: signal?.type || 'unknown',
            timestamp: trade.exitTime
        });
        
        // Update dashboard immediately
        this.updateDashboard();
        
        // Log status update
        setTimeout(() => this.logStatus(), 1000);
    }

    /**
     * Stop the velocity trading bot
     */
    async stop() {
        console.log('🛑 Stopping Velocity Trading Bot V2.1...');
        
        this.isRunning = false;
        
        // Stop position monitoring
        this.positionManager.stopMonitoring();
        
        // Clear timers
        if (this.dataUpdateTimer) {
            clearInterval(this.dataUpdateTimer);
            this.dataUpdateTimer = null;
        }
        
        if (this.dashboardUpdateTimer) {
            clearInterval(this.dashboardUpdateTimer);
            this.dashboardUpdateTimer = null;
        }
        
        // Force exit any open position
        if (this.tradingEngine.isInTrade) {
            console.log('🏁 Force closing open position...');
            await this.positionManager.forceExit('Bot shutdown');
        }
        
        // Stop web server
        await this.webServer.stop();
        
        // Final statistics
        const finalStats = this.positionManager.getStatistics();
        console.log('\n📊 FINAL VELOCITY TRADING STATISTICS:');
        console.log(`   💰 Final Balance: $${finalStats.currentBalance.toFixed(2)}`);
        console.log(`   📈 Total Trades: ${finalStats.totalTrades}`);
        console.log(`   🎯 Win Rate: ${finalStats.winRate.toFixed(1)}%`);
        console.log(`   💵 Total P&L: ${finalStats.netProfit > 0 ? '+' : ''}$${finalStats.netProfit.toFixed(2)}`);
        console.log(`   📊 Return: ${((finalStats.currentBalance - 100) / 100 * 100).toFixed(2)}%`);
        console.log(`   ⚡ Total Signals: ${finalStats.totalSignals}`);
        console.log(`   🚀 Velocity Trades: ${finalStats.velocityTrades}`);
        console.log(`   🎯 Thrust Trades: ${finalStats.thrustTrades}`);
        console.log(`   📊 Signal Accuracy: ${finalStats.signalAccuracy.toFixed(1)}%`);
        
        // Export trades to CSV
        this.tradeLogger.exportToCSV(`velocity_trades_${new Date().toISOString().split('T')[0]}.csv`);
        
        console.log('✅ Velocity Trading Bot V2.1 stopped gracefully');
    }

    /**
     * Get current bot statistics
     * @returns {Object} Bot statistics
     */
    getStatistics() {
        return {
            ...this.positionManager.getStatistics(),
            ...this.tradeLogger.getStatistics(),
            isRunning: this.isRunning,
            lastUpdate: this.lastUpdate,
            topGainersCount: this.currentTopGainers.length,
            botVersion: 'V2.1-Velocity'
        };
    }

    /**
     * Manual trade execution (for testing)
     * @param {string} symbol - Symbol to trade
     * @param {number} price - Entry price
     * @param {string} type - Signal type ('VELOCITY' or 'THRUST')
     */
    async manualTrade(symbol, price, type = 'MANUAL') {
        if (!this.tradingEngine.canTrade()) {
            console.log('❌ Cannot execute manual trade: Already in position or insufficient balance');
            return;
        }

        console.log(`🔧 Manual ${type} trade execution: ${symbol} @ $${price}`);

        const result = this.tradingEngine.enterPosition(symbol, price);
        if (result.success) {
            console.log('✅ Manual trade executed successfully');

            // Add signal type to position
            if (this.tradingEngine.currentPosition) {
                this.tradingEngine.currentPosition.entryType = type;
            }
        } else {
            console.log(`❌ Manual trade failed: ${result.message}`);
        }
    }

    /**
     * Change trading strategy
     * @param {string} strategy - 'VELOCITY', 'THRUST', or 'BOTH'
     */
    setStrategy(strategy) {
        const success = this.velocityDetector.setStrategy(strategy);
        if (success) {
            console.log(`🔄 Trading strategy changed to: ${strategy}`);
            return true;
        }
        return false;
    }

    /**
     * Reset trading bot (balance, logs, statistics)
     */
    resetBot() {
        console.log('🔄 Resetting trading bot...');

        // Reset trading engine
        this.tradingEngine.reset();

        // Reset velocity detector
        this.velocityDetector.reset();

        // Reset trade logger
        this.tradeLogger = new TradeLogger();

        console.log('✅ Trading bot reset complete - fresh start with $100');
    }

    /**
     * Check if bot is running
     * @returns {boolean} True if bot is running
     */
    isRunning() {
        return this.botRunning;
    }

    /**
     * Stop the trading bot
     */
    async stop() {
        if (!this.botRunning) {
            console.log('⚠️ Bot is already stopped');
            return;
        }

        console.log('🛑 Stopping Velocity Trading Bot...');

        // Stop all timers
        if (this.dataUpdateTimer) {
            clearInterval(this.dataUpdateTimer);
            this.dataUpdateTimer = null;
        }

        if (this.tradingUpdateTimer) {
            clearInterval(this.tradingUpdateTimer);
            this.tradingUpdateTimer = null;
        }

        if (this.dashboardUpdateTimer) {
            clearInterval(this.dashboardUpdateTimer);
            this.dashboardUpdateTimer = null;
        }

        // Force close any open position
        if (this.positionManager && this.tradingEngine.isInTrade) {
            console.log('🏁 Force closing open position...');
            await this.positionManager.forceExit('Bot stopped');
        }

        this.botRunning = false;
        console.log('✅ Velocity Trading Bot stopped successfully');
    }

    /**
     * Restart the trading bot
     */
    async restart() {
        console.log('🔄 Restarting Velocity Trading Bot...');

        if (this.botRunning) {
            await this.stop();
            // Wait a moment before restarting
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        await this.start();
    }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Received SIGINT, shutting down velocity bot gracefully...');
    if (global.velocityTradingBot) {
        await global.velocityTradingBot.stop();
    }
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Received SIGTERM, shutting down velocity bot gracefully...');
    if (global.velocityTradingBot) {
        await global.velocityTradingBot.stop();
    }
    process.exit(0);
});

// Start the velocity trading bot
async function main() {
    try {
        const bot = new VelocityTradingBot();
        global.velocityTradingBot = bot;
        await bot.start();
        
        // Log status every 2 minutes (more frequent for velocity trading)
        setInterval(() => {
            if (bot.isRunning) {
                bot.logStatus();
            }
        }, 120000); // 2 minutes
        
    } catch (error) {
        console.error('💥 Fatal error:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = VelocityTradingBot;
