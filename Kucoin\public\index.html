<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KuCoin Top Gainers Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-content">
                <div class="logo-section">
                    <i class="fas fa-rocket"></i>
                    <h1>KuCoin Top Gainers</h1>
                    <span class="version">v1.0</span>
                </div>
                <div class="status-section">
                    <div class="status-item">
                        <i class="fas fa-circle status-indicator" id="connectionStatus"></i>
                        <span id="connectionText">Connecting...</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-users"></i>
                        <span id="clientCount">0</span> clients
                    </div>
                    <div class="status-item">
                        <i class="fas fa-clock"></i>
                        <span id="lastUpdate">Never</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="topGainerPercent">0%</h3>
                        <p>Top Gainer</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalGainers">0</h3>
                        <p>Total Gainers</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="updateCount">0</h3>
                        <p>Updates</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalVolume">+0.00%</h3>
                        <p>Avg 30min Momentum</p>
                    </div>
                </div>
            </div>

            <!-- Controls -->
            <div class="controls-section">
                <div class="controls-left">
                    <h2><i class="fas fa-trophy"></i> Top 10 Gainers</h2>
                    <div class="strategy-toggle">
                        <button class="btn btn-strategy active" id="strategy24h" onclick="switchStrategy('PRICE_24H')">
                            📊 24h Price (Proven)
                        </button>
                        <button class="btn btn-strategy" id="strategy30min" onclick="switchStrategy('PRICE_30MIN')">
                            ⚡ 30min Price (Experimental)
                        </button>
                    </div>
                </div>
                <div class="controls-right">
                    <button class="btn btn-primary" onclick="refreshData()">
                        <i class="fas fa-refresh"></i> Refresh
                    </button>
                    <button class="btn btn-secondary" onclick="toggleAutoRefresh()">
                        <i class="fas fa-play" id="autoRefreshIcon"></i>
                        <span id="autoRefreshText">Auto: ON</span>
                    </button>
                </div>
            </div>

            <!-- Gainers Table -->
            <div class="table-container">
                <div class="table-wrapper">
                    <table class="gainers-table" id="gainersTable">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Symbol</th>
                                <th>Price</th>
                                <th>24h Change</th>
                                <th>30min Change</th>
                                <th>High/Low</th>
                                <th>Last Update</th>
                            </tr>
                        </thead>
                        <tbody id="gainersTableBody">
                            <tr class="loading-row">
                                <td colspan="7">
                                    <div class="loading-spinner">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        Loading top gainers...
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Update Info -->
            <div class="update-info">
                <div class="update-item">
                    <i class="fas fa-info-circle"></i>
                    <span>Price updates every 1.5 seconds</span>
                </div>
                <div class="update-item">
                    <i class="fas fa-list"></i>
                    <span>Top gainers list updates every 2 minutes</span>
                </div>
                <div class="update-item">
                    <i class="fas fa-filter"></i>
                    <span>Showing USDT & BTC pairs only</span>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="dashboard-footer">
            <div class="footer-content">
                <p>&copy; 2025 KuCoin Top Gainers Bot - Real-time Cryptocurrency Dashboard</p>
                <div class="footer-links">
                    <span>Data from KuCoin API</span>
                    <span>•</span>
                    <span>Updates in real-time</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <script src="dashboard.js"></script>
</body>
</html>
