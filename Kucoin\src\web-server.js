const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const cors = require('cors');

/**
 * Web Server for KuCoin Top Gainers Dashboard
 * Serves the web dashboard and provides real-time updates via WebSocket
 */
class WebServer {
    constructor(port = 3000, tradingBot = null) {
        this.port = port;
        this.tradingBot = tradingBot;
        this.app = express();
        this.server = http.createServer(this.app);
        this.io = socketIo(this.server, {
            cors: {
                origin: "*",
                methods: ["GET", "POST"]
            }
        });
        
        // Data storage
        this.currentTopGainers = [];
        this.lastUpdate = null;
        this.updateCount = 0;
        this.connectedClients = 0;
        
        this.setupMiddleware();
        this.setupRoutes();
        this.setupWebSocket();
    }

    /**
     * Setup Express middleware
     */
    setupMiddleware() {
        // Enable CORS
        this.app.use(cors());
        
        // Parse JSON bodies
        this.app.use(express.json());
        
        // Serve static files from public directory
        this.app.use(express.static(path.join(__dirname, '..', 'public')));
        
        // Logging middleware
        this.app.use((req, res, next) => {
            console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
            next();
        });
    }

    /**
     * Setup API routes
     */
    setupRoutes() {
        // Serve the main dashboard
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, '..', 'public', 'index.html'));
        });

        // Serve the trading dashboard
        this.app.get('/trading', (req, res) => {
            res.sendFile(path.join(__dirname, '..', 'public', 'trading.html'));
        });

        // API endpoint to get current top gainers
        this.app.get('/api/gainers', (req, res) => {
            res.json({
                success: true,
                data: {
                    gainers: this.currentTopGainers,
                    lastUpdate: this.lastUpdate,
                    updateCount: this.updateCount,
                    connectedClients: this.connectedClients
                }
            });
        });

        // API endpoint to get server status
        this.app.get('/api/status', (req, res) => {
            res.json({
                success: true,
                data: {
                    status: 'running',
                    uptime: process.uptime(),
                    lastUpdate: this.lastUpdate,
                    updateCount: this.updateCount,
                    connectedClients: this.connectedClients,
                    totalGainers: this.currentTopGainers.length
                }
            });
        });

        // Health check endpoint
        this.app.get('/health', (req, res) => {
            res.json({ status: 'healthy', timestamp: new Date().toISOString() });
        });

        // Trading bot control endpoints (if bot is available)
        if (this.tradingBot) {
            // Get trading statistics
            this.app.get('/api/trading/status', (req, res) => {
                try {
                    const stats = this.tradingBot.getStatistics();
                    res.json({
                        success: true,
                        data: stats
                    });
                } catch (error) {
                    res.status(500).json({
                        success: false,
                        error: 'Failed to get trading status',
                        details: error.message
                    });
                }
            });

            // Get recent trades
            this.app.get('/api/trading/trades', (req, res) => {
                try {
                    const trades = this.tradingBot.getRecentTrades();
                    res.json({
                        success: true,
                        data: trades
                    });
                } catch (error) {
                    res.status(500).json({
                        success: false,
                        error: 'Failed to get trades',
                        details: error.message
                    });
                }
            });

            // Change trading strategy
            this.app.post('/api/strategy', (req, res) => {
                const { strategy } = req.body;

                if (!strategy || !['VELOCITY', 'THRUST'].includes(strategy)) {
                    return res.status(400).json({
                        success: false,
                        error: 'Invalid strategy. Must be VELOCITY or THRUST'
                    });
                }

                try {
                    const success = this.tradingBot.setStrategy(strategy);
                    if (success) {
                        res.json({
                            success: true,
                            message: `Strategy changed to ${strategy}`,
                            strategy: strategy
                        });
                    } else {
                        res.status(500).json({
                            success: false,
                            error: 'Failed to change strategy'
                        });
                    }
                } catch (error) {
                    res.status(500).json({
                        success: false,
                        error: 'Error changing strategy',
                        details: error.message
                    });
                }
            });

            // Change top gainers strategy
            this.app.post('/api/top-gainers-strategy', (req, res) => {
                const { strategy } = req.body;

                if (!strategy || !['PRICE_24H', 'PRICE_30MIN'].includes(strategy)) {
                    return res.status(400).json({
                        success: false,
                        error: 'Invalid strategy. Must be PRICE_24H or PRICE_30MIN'
                    });
                }

                try {
                    // Update the strategy in data processor
                    const success = this.tradingBot.dataProcessor.setStrategy(strategy);

                    if (success) {
                        console.log(`🔄 Top gainers strategy changed to: ${strategy}`);

                        res.json({
                            success: true,
                            message: `Top gainers strategy changed to ${strategy}`,
                            strategy: strategy
                        });
                    } else {
                        res.status(400).json({
                            success: false,
                            error: 'Invalid strategy provided'
                        });
                    }
                } catch (error) {
                    res.status(500).json({
                        success: false,
                        error: 'Error changing top gainers strategy',
                        details: error.message
                    });
                }
            });

            // Start trading bot
            this.app.post('/api/start', (req, res) => {
                try {
                    if (this.tradingBot.isRunning && this.tradingBot.isRunning()) {
                        return res.json({
                            success: false,
                            error: 'Bot is already running'
                        });
                    }

                    // Start the bot
                    if (this.tradingBot.start) {
                        this.tradingBot.start();
                        res.json({
                            success: true,
                            message: 'Bot started successfully'
                        });
                    } else {
                        res.status(500).json({
                            success: false,
                            error: 'Bot start method not available'
                        });
                    }
                } catch (error) {
                    res.status(500).json({
                        success: false,
                        error: 'Failed to start bot',
                        details: error.message
                    });
                }
            });

            // Stop trading bot
            this.app.post('/api/stop', (req, res) => {
                try {
                    if (this.tradingBot.isRunning && !this.tradingBot.isRunning()) {
                        return res.json({
                            success: false,
                            error: 'Bot is already stopped'
                        });
                    }

                    // Stop the bot
                    if (this.tradingBot.stop) {
                        this.tradingBot.stop();
                        res.json({
                            success: true,
                            message: 'Bot stopped successfully'
                        });
                    } else {
                        res.status(500).json({
                            success: false,
                            error: 'Bot stop method not available'
                        });
                    }
                } catch (error) {
                    res.status(500).json({
                        success: false,
                        error: 'Failed to stop bot',
                        details: error.message
                    });
                }
            });

            // Reset trading bot
            this.app.post('/api/reset', (req, res) => {
                try {
                    this.tradingBot.resetBot();
                    res.json({
                        success: true,
                        message: 'Bot reset successfully',
                        balance: 100,
                        trades: 0
                    });
                } catch (error) {
                    res.status(500).json({
                        success: false,
                        error: 'Failed to reset bot',
                        details: error.message
                    });
                }
            });
        }

        // 404 handler
        this.app.use((req, res) => {
            res.status(404).json({ error: 'Not found' });
        });
    }

    /**
     * Setup WebSocket connections
     */
    setupWebSocket() {
        this.io.on('connection', (socket) => {
            this.connectedClients++;
            console.log(`🔌 Client connected. Total clients: ${this.connectedClients}`);

            // Send current data to new client
            socket.emit('gainers-update', {
                gainers: this.currentTopGainers,
                lastUpdate: this.lastUpdate,
                updateCount: this.updateCount
            });

            // Send status update
            socket.emit('status-update', {
                connectedClients: this.connectedClients,
                lastUpdate: this.lastUpdate,
                updateCount: this.updateCount
            });

            // Handle client disconnect
            socket.on('disconnect', () => {
                this.connectedClients--;
                console.log(`🔌 Client disconnected. Total clients: ${this.connectedClients}`);
            });

            // Handle client requesting refresh
            socket.on('request-refresh', () => {
                socket.emit('gainers-update', {
                    gainers: this.currentTopGainers,
                    lastUpdate: this.lastUpdate,
                    updateCount: this.updateCount
                });
            });
        });
    }

    /**
     * Update top gainers data and broadcast to all clients
     * @param {Array} gainers - Array of top gainer objects
     */
    updateTopGainers(gainers) {
        this.currentTopGainers = gainers;
        this.lastUpdate = new Date().toISOString();
        this.updateCount++;

        // Broadcast to all connected clients
        this.io.emit('gainers-update', {
            gainers: this.currentTopGainers,
            lastUpdate: this.lastUpdate,
            updateCount: this.updateCount
        });

        console.log(`📊 Updated ${gainers.length} gainers, broadcasting to ${this.connectedClients} clients`);
    }

    /**
     * Update prices and broadcast to all clients
     * @param {Array} gainers - Array of top gainer objects with updated prices
     */
    updatePrices(gainers) {
        this.currentTopGainers = gainers;
        this.lastUpdate = new Date().toISOString();

        // Broadcast price update to all connected clients
        this.io.emit('prices-update', {
            gainers: this.currentTopGainers,
            lastUpdate: this.lastUpdate
        });
    }

    /**
     * Broadcast status update to all clients
     * @param {Object} status - Status object
     */
    broadcastStatus(status) {
        this.io.emit('status-update', {
            ...status,
            connectedClients: this.connectedClients,
            lastUpdate: this.lastUpdate,
            updateCount: this.updateCount
        });
    }

    /**
     * Start the web server
     * @returns {Promise} Promise that resolves when server is started
     */
    start() {
        return new Promise((resolve, reject) => {
            this.server.listen(this.port, (err) => {
                if (err) {
                    reject(err);
                } else {
                    console.log(`🌐 Web dashboard server started on http://localhost:${this.port}`);
                    console.log(`📊 Dashboard URL: http://localhost:${this.port}`);
                    resolve();
                }
            });
        });
    }

    /**
     * Stop the web server
     */
    stop() {
        return new Promise((resolve) => {
            this.server.close(() => {
                console.log('🌐 Web server stopped');
                resolve();
            });
        });
    }

    /**
     * Get server statistics
     * @returns {Object} Server statistics
     */
    getStats() {
        return {
            connectedClients: this.connectedClients,
            lastUpdate: this.lastUpdate,
            updateCount: this.updateCount,
            totalGainers: this.currentTopGainers.length,
            uptime: process.uptime()
        };
    }
}

module.exports = WebServer;
